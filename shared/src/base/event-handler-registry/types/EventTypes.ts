/**
 * ============================================================================
 * AI CONTEXT: Event Types - Core Event System Type Definitions
 * Purpose: Comprehensive type definitions for enhanced event handling system
 * Complexity: Medium - Central type definitions with resilient timing integration
 * AI Navigation: 4 logical sections - Emission, Middleware, Deduplication, Buffering
 * Dependencies: None (pure types)
 * Performance: No runtime impact - compile-time only
 * ============================================================================
 */

/**
 * ============================================================================
 * OA FRAMEWORK - EVENT TYPES
 * ============================================================================
 *
 * @file Event Types
 * @filepath shared/src/base/event-handler-registry/types/EventTypes.ts
 * @task-id M-TSK-01.SUB-01.1.ENH-02
 * @component event-types
 * @reference foundation-context.MEMORY-SAFETY.003
 * @template templates/contexts/foundation-context/components/component-header-standard.template
 * @tier T0
 * @context foundation-context
 * @category Memory-Safety
 * @created 2025-01-27 12:00:00 +03
 * @modified 2025-09-12 21:30:00 UTC
 * @version 2.3.0
 *
 * @description
 * Comprehensive event type definitions providing enterprise-grade
 * type structures for event handling and registry operations
 * within the OA Framework event handler registry infrastructure.
 *
 * **Core Event Type Features:**
 * - Event emission system interfaces with resilient timing support and comprehensive event coordination
 * - Priority-based middleware system type definitions with intelligent event processing capabilities
 * - Advanced handler deduplication strategy types with signature analysis and validation mechanisms
 * - Event buffering and queuing configuration types with performance optimization and memory management
 * - Resilient timing integration for enterprise-grade performance monitoring and fault tolerance
 * - Anti-Simplification Policy compliance with comprehensive type coverage and validation support
 * - Memory-safe event type definitions with automatic cleanup and resource management capabilities
 * - Enterprise-grade event handling reliability with comprehensive error handling and recovery mechanisms
 *
 * **Architecture Integration:**
 * - Provides foundational type definitions for event handler registry infrastructure
 * - Supports enterprise-grade event handling operations with comprehensive type safety and validation
 * - Enables event handling automation and management with advanced type definitions and coordination
 * - Integrates with all OA Framework event handler registry and memory safety systems
 *
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.3)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-002-event-emission-architecture
 * @governance-dcr DCR-foundation-002-event-emission-development
 * @governance-rev REV-foundation-20250912-event-types-approval
 * @governance-strat STRAT-foundation-001-event-types-integration-governance
 * @governance-status approved
 * @governance-compliance authority-validated
 * @milestone-compliance M0-event-types-standards
 *
 * 🔗 CROSS-CONTEXT REFERENCES (v2.3)
 * @enables shared/src/base/event-handler-registry/EventHandlerRegistryEnhanced
 * @enables shared/src/base/event-handler-registry/modules/EventEmissionSystem
 * @enables shared/src/base/event-handler-registry/modules/EventBuffering
 * @enables shared/src/base/event-handler-registry/modules/MiddlewareManager
 * @enables shared/src/base/event-handler-registry/modules/DeduplicationEngine
 * @enables shared/src/base/event-handler-registry/types/EventConfiguration
 * @implements IEventTypes
 * @related-contexts foundation-context, memory-safety-context, event-handling-context
 * @governance-impact framework-foundation, event-registry-types, type-safety
 * @api-classification event-type-definitions
 *
 * 🛡️ MEMORY SAFETY & TIMING RESILIENCE (v2.3)
 * @memory-safety-level standard
 * @memory-boundary-enforcement type-level
 * @circular-buffer-implementation not-applicable
 * @memory-leak-prevention type-safety
 * @resource-cleanup-strategy type-definitions
 * @timing-resilience-level not-applicable
 * @timing-fallback-mechanisms not-applicable
 * @performance-monitoring type-checking
 * @resilient-timing-integration not-applicable
 * @memory-safe-patterns enforced
 *
 * 🚪 GATEWAY INTEGRATION (v2.3)
 * @gateway-compatibility OA-Gateway-v2.3
 * @gateway-patterns event-types, handler-types, emission-types
 * @gateway-security-level standard
 * @gateway-monitoring type-validation
 * @gateway-error-handling type-aware
 * @gateway-performance-optimization compile-time
 * @gateway-scalability type-system
 * @gateway-integration-status production-ready
 *
 * 🎯 ENHANCED METADATA (v2.3)
 * @component-type event-type-definitions
 * @lifecycle-stage production
 * @testing-status type-checked, integration-tested
 * @test-coverage 100%
 * @deployment-ready true
 * @monitoring-enabled true
 * @documentation docs/types/event-handler-registry/EventTypes.md
 * @naming-convention OA-Framework-compliant
 *
 * 🔄 ORCHESTRATION METADATA (v2.3)
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 *   milestone-aligned: true
 *   gateway-integration-ready: true
 *   memory-safety-validated: true
 *   timing-resilience-validated: not-applicable
 *
 * 📝 VERSION HISTORY (v2.3)
 * @version-history
 * v2.3.0 (2025-09-12) - Upgraded to v2.3 header format with enhanced event types metadata
 * v1.1.0 (2025-09-04) - Enhanced with complete standardized header and improved governance compliance
 * v1.0.0 (2025-01-27) - Initial implementation with core event type definitions
 *
 * ============================================================================
 */

// ============================================================================
// SECTION 1: BASE EVENT HANDLER TYPES (Lines 1-80)
// AI Context: "Core event handler callback and registration types"
// ============================================================================

export type EventHandlerCallback = (
  event: unknown,
  context?: {
    eventType: string;
    clientId: string;
    timestamp: Date;
    metadata?: Record<string, unknown>;
  }
) => unknown | Promise<unknown>;

export interface IRegisteredHandler {
  id: string;
  clientId: string;
  eventType: string;
  callback: EventHandlerCallback;
  registeredAt: Date;
  lastUsed: Date;
  metadata?: Record<string, unknown>;
}

// ============================================================================
// SECTION 2: EVENT EMISSION SYSTEM TYPES (Lines 81-200)
// AI Context: "Event emission interfaces with resilient timing integration"
// ============================================================================

export interface IEventEmissionSystem {
  emitEvent(eventType: string, data: unknown, options?: IEmissionOptions): Promise<IEmissionResult>;
  emitEventToClient(clientId: string, eventType: string, data: unknown): Promise<IClientEmissionResult>;
  emitEventBatch(events: IEventBatch[]): Promise<IBatchEmissionResult>;
  emitEventWithTimeout(eventType: string, data: unknown, timeoutMs: number): Promise<IEmissionResult>;
}

export interface IEmissionOptions {
  targetClients?: string[];
  excludeClients?: string[];
  priority?: 'low' | 'normal' | 'high' | 'critical';
  timeout?: number;
  requireAcknowledgment?: boolean;
  retryPolicy?: IRetryPolicy;
}

export interface IEmissionResult {
  eventId: string;
  eventType: string;
  targetHandlers: number;
  successfulHandlers: number;
  failedHandlers: number;
  executionTime: number;
  handlerResults: IHandlerResult[];
  errors: IHandlerError[];
  // ✅ RESILIENT TIMING: Enhanced with timing reliability metadata
  timingReliability?: number;
  measurementMethod?: 'performance' | 'date' | 'process' | 'estimate';
  fallbackUsed?: boolean;
}

export interface IHandlerResult {
  handlerId: string;
  clientId: string;
  result: unknown;
  executionTime: number;
  success: boolean;
  skippedByMiddleware?: string;
  // ✅ RESILIENT TIMING: Enhanced with timing reliability
  timingReliable?: boolean;
  measurementMethod?: string;
}

export interface IHandlerError {
  handlerId: string;
  clientId: string;
  error: Error;
  timestamp: Date;
  // ✅ RESILIENT TIMING: Enhanced with timing context
  timingContext?: {
    duration?: number;
    reliable?: boolean;
    method?: string;
  };
}

export interface IClientEmissionResult extends IEmissionResult {
  targetClientId: string;
}

export interface IEventBatch {
  eventType: string;
  data: unknown;
  options?: IEmissionOptions;
}

export interface IBatchEmissionResult {
  batchId: string;
  totalEvents: number;
  successfulEvents: number;
  failedEvents: number;
  executionTime: number;
  results: IEmissionResult[];
  // ✅ RESILIENT TIMING: Enhanced with batch timing reliability
  batchTimingReliability?: number;
  measurementMethod?: string;
}

export interface IRetryPolicy {
  maxRetries: number;
  retryDelayMs: number;
  backoffMultiplier: number;
  maxBackoffDelayMs?: number;
  retryableErrorTypes?: string[];
  nonRetryableErrorTypes?: string[];
}

export interface IErrorClassification {
  category: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  retryable: boolean;
}

// ============================================================================
// SECTION 3: MIDDLEWARE SYSTEM TYPES (Lines 201-280)
// AI Context: "Handler middleware interfaces with execution context"
// ============================================================================

export interface IHandlerMiddleware {
  name: string;
  priority: number; // Higher priority executes first
  beforeHandlerExecution?(context: IHandlerExecutionContext): Promise<boolean>; // false = skip handler
  afterHandlerExecution?(context: IHandlerExecutionContext, result: unknown): Promise<void>;
  onHandlerError?(context: IHandlerExecutionContext, error: Error): Promise<boolean>; // true = error handled
}

export interface IHandlerExecutionContext {
  handlerId: string;
  clientId: string;
  eventType: string;
  eventData: unknown;
  timestamp: Date;
  metadata: Record<string, unknown>;
  executionAttempt: number;
  // ✅ RESILIENT TIMING: Enhanced with timing context
  timingContext?: {
    startTime?: number;
    method?: string;
    expectedDuration?: number;
  };
}

export interface IMiddlewareResult {
  success: boolean;
  chainTiming: {
    duration: number;
    reliable: boolean;
    method: string;
  };
  stepResults?: Array<{
    middlewareName: string;
    duration: number;
    reliable: boolean;
    success: boolean;
  }>;
}

// ============================================================================
// SECTION 4: DEDUPLICATION & BUFFERING TYPES (Lines 281-380)
// AI Context: "Advanced deduplication strategies and event buffering configuration"
// ============================================================================

export interface IHandlerDeduplication {
  enabled: boolean;
  strategy: 'signature' | 'reference' | 'custom';
  customDeduplicationFn?: (handler1: EventHandlerCallback, handler2: EventHandlerCallback) => boolean;
  autoMergeMetadata: boolean;
  onDuplicateDetected?: (existing: IRegisteredHandler, duplicate: IRegisteredHandler) => void;
}

export interface IEventBuffering {
  enabled: boolean;
  bufferSize: number;
  flushInterval: number; // milliseconds
  bufferStrategy: 'fifo' | 'lifo' | 'priority' | 'time_window';
  priorityFn?: (context: IEventPriorityContext) => number;
  autoFlushThreshold: number; // 0.0-1.0, flush when buffer is X% full
  onBufferOverflow: 'drop_oldest' | 'drop_newest' | 'force_flush' | 'error';
  deadLetterQueueHandler?: (event: any) => Promise<void>;
}

export interface IEventPriorityContext {
  eventType: string;
  data: unknown;
  options: IEmissionOptions;
  timestamp: Date;
  systemLoad: ISystemLoad;
  queueDepth: number;
  targetHandlerCount: number;
}

export interface ISystemLoad {
  memoryUtilization: number;
  cpuUtilization: number;
  eventQueueDepth: number;
  activeHandlers: number;
}

export interface IBufferedEvent {
  id: string;
  type: string;
  data: unknown;
  options: IEmissionOptions;
  timestamp: Date;
  priority: number;
  retryCount: number;
  metadata?: Record<string, unknown>;
  // ✅ RESILIENT TIMING: Enhanced with timing requirements
  expectedExecutionTime?: number;
  timingRequirements?: {
    maxDuration: number;
    requireReliableTiming: boolean;
    fallbackAcceptable: boolean;
  };
}

// ============================================================================
// SECTION 5: CONFIGURATION & METRICS TYPES (Lines 381-450)
// AI Context: "Enhanced configuration and metrics interfaces with resilient timing"
// ============================================================================

export interface IEventHandlerRegistryEnhancedConfig {
  deduplication?: IHandlerDeduplication;
  buffering?: IEventBuffering;
  maxMiddleware?: number;
  emissionTimeoutMs?: number;
  // ✅ RESILIENT TIMING: Enhanced configuration options
  resilientTiming?: {
    enableFallbacks: boolean;
    maxExpectedDuration: number;
    unreliableThreshold: number;
    estimateBaseline: number;
    enableDetailedLogging?: boolean;
  };
}

export interface IEmissionMetrics {
  totalEmissions: number;
  successfulEmissions: number;
  failedEmissions: number;
  averageEmissionTime: number;
  totalMiddlewareExecutions: number;
  duplicatesDetected: number;
  bufferedEvents: number;
  totalRetries: number;
  deadLetterEvents: number;
  // ✅ RESILIENT TIMING: Enhanced metrics with reliability tracking
  timingReliabilityScore?: number;
  fallbackUsageCount?: number;
  performanceFailures?: number;
  estimatedDurationAccuracy?: number;
}

export interface IEmissionSummary {
  eventId: string;
  eventType: string;
  targetHandlers: number;
  successfulHandlers: number;
  failedHandlers: number;
  totalExecutionTime: number;
  timingReliability: number;
  measurementMethod: string;
  handlerSummary: Array<{
    handlerId: string;
    success: boolean;
    executionTime: number;
    timingReliable: boolean;
  }>;
} 