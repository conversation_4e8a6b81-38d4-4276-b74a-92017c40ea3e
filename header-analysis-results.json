{"total": 471, "byHeaderStatus": {"minimal-header": 24, "v2.0-or-older": 34, "v2.1": 177, "v2.3": 24, "no-header": 212}, "byTier": {"server": 134, "shared": 100, "unknown": 10, "docs": 227}, "byCategory": {"typescript": 233, "documentation": 235, "javascript": 3}, "byPriority": {"critical": 84, "high": 113, "medium": 37, "low": 237}, "largeFiles": [{"path": "./server/src/platform/integration/core-bridge/RealtimeEventCoordinator.ts", "lines": 2657, "headerStatus": "minimal-header", "priority": "critical"}, {"path": "./server/src/platform/integration/core-bridge/AuthorityComplianceMonitorBridge.ts", "lines": 2324, "headerStatus": "minimal-header", "priority": "critical"}, {"path": "./server/src/platform/integration/core-bridge/CrossReferenceValidationBridge.ts", "lines": 1877, "headerStatus": "v2.0-or-older", "priority": "critical"}, {"path": "./server/src/platform/integration/core-bridge/GovernanceTrackingBridge.ts", "lines": 3087, "headerStatus": "v2.1", "priority": "critical"}, {"path": "./server/src/platform/tracking/advanced-data/SmartPathResolutionSystem.ts", "lines": 924, "headerStatus": "v2.1", "priority": "high"}, {"path": "./server/src/platform/tracking/advanced-data/ContextAuthorityProtocol.ts", "lines": 1431, "headerStatus": "v2.1", "priority": "high"}, {"path": "./server/src/platform/tracking/advanced-data/CrossReferenceValidationEngine.ts", "lines": 1259, "headerStatus": "v2.1", "priority": "high"}, {"path": "./server/src/platform/tracking/advanced-data/OrchestrationCoordinator.ts", "lines": 1280, "headerStatus": "v2.1", "priority": "high"}, {"path": "./server/src/platform/tracking/core-data/GovernanceLogTracker.ts", "lines": 1847, "headerStatus": "v2.0-or-older", "priority": "critical"}, {"path": "./server/src/platform/tracking/core-data/ImplementationProgressTracker.ts", "lines": 1484, "headerStatus": "v2.0-or-older", "priority": "critical"}, {"path": "./server/src/platform/tracking/core-data/base/BaseTrackingService.ts", "lines": 1977, "headerStatus": "v2.0-or-older", "priority": "critical"}, {"path": "./server/src/platform/tracking/core-data/AnalyticsCacheManager.ts", "lines": 2030, "headerStatus": "v2.0-or-older", "priority": "critical"}, {"path": "./server/src/platform/tracking/core-data/SessionLogTracker.ts", "lines": 2818, "headerStatus": "v2.0-or-older", "priority": "critical"}, {"path": "./server/src/platform/tracking/core-utils/TrackingUtilities.ts", "lines": 1031, "headerStatus": "v2.1", "priority": "critical"}, {"path": "./server/src/platform/tracking/core-managers/TrackingManager.ts", "lines": 1082, "headerStatus": "v2.1", "priority": "critical"}, {"path": "./server/src/platform/tracking/core-managers/DashboardManager.ts", "lines": 1332, "headerStatus": "v2.1", "priority": "critical"}, {"path": "./server/src/platform/tracking/core-managers/FileManager.ts", "lines": 880, "headerStatus": "v2.1", "priority": "critical"}, {"path": "./server/src/platform/tracking/core-managers/RealTimeManager.ts", "lines": 1735, "headerStatus": "v2.1", "priority": "critical"}, {"path": "./server/src/platform/tracking/core-trackers/AnalyticsTrackingEngine.ts", "lines": 747, "headerStatus": "v2.3", "priority": "critical"}, {"path": "./server/src/platform/tracking/core-trackers/SessionTrackingCore.ts", "lines": 735, "headerStatus": "v2.1", "priority": "critical"}, {"path": "./server/src/platform/tracking/core-trackers/GovernanceTrackingSystem.ts", "lines": 1413, "headerStatus": "v2.3", "priority": "critical"}, {"path": "./server/src/platform/tracking/core-trackers/ProgressTrackingEngine.ts", "lines": 978, "headerStatus": "v2.1", "priority": "critical"}, {"path": "./server/src/platform/governance/analytics-engines/GovernanceRuleAnalyticsEngine.ts", "lines": 1434, "headerStatus": "v2.1", "priority": "high"}, {"path": "./server/src/platform/governance/analytics-engines/GovernanceRuleReportingEngine.ts", "lines": 1174, "headerStatus": "minimal-header", "priority": "high"}, {"path": "./server/src/platform/governance/analytics-engines/GovernanceRuleInsightsGenerator.ts", "lines": 1074, "headerStatus": "v2.1", "priority": "high"}, {"path": "./server/src/platform/governance/analytics-engines/GovernanceRuleOptimizationEngine.ts", "lines": 1578, "headerStatus": "v2.1", "priority": "high"}, {"path": "./server/src/platform/governance/security-management/RuleSecurityManager.ts", "lines": 807, "headerStatus": "v2.1", "priority": "high"}, {"path": "./server/src/platform/governance/security-management/RuleSecurityFramework.ts", "lines": 738, "headerStatus": "v2.1", "priority": "high"}, {"path": "./server/src/platform/governance/security-management/RuleIntegrityValidator.ts", "lines": 719, "headerStatus": "v2.1", "priority": "high"}, {"path": "./server/src/platform/governance/management-configuration/GovernanceRuleTemplateEngine.ts", "lines": 1627, "headerStatus": "v2.1", "priority": "high"}, {"path": "./server/src/platform/governance/management-configuration/GovernanceRuleConfigurationManager.ts", "lines": 1169, "headerStatus": "v2.1", "priority": "high"}, {"path": "./server/src/platform/governance/management-configuration/GovernanceRuleCSRFManager.ts", "lines": 860, "headerStatus": "v2.3", "priority": "high"}, {"path": "./server/src/platform/governance/management-configuration/GovernanceRuleDocumentationGenerator.ts", "lines": 1222, "headerStatus": "v2.1", "priority": "high"}, {"path": "./server/src/platform/governance/management-configuration/GovernanceRuleInputValidator.ts", "lines": 1201, "headerStatus": "v2.1", "priority": "high"}, {"path": "./server/src/platform/governance/management-configuration/GovernanceRuleTemplateSecurity.ts", "lines": 823, "headerStatus": "v2.1", "priority": "high"}, {"path": "./server/src/platform/governance/management-configuration/GovernanceRuleEnvironmentManager.ts", "lines": 1172, "headerStatus": "v2.1", "priority": "high"}, {"path": "./server/src/platform/governance/reporting-infrastructure/GovernanceRuleComplianceReporter.ts", "lines": 1773, "headerStatus": "v2.1", "priority": "high"}, {"path": "./server/src/platform/governance/reporting-infrastructure/GovernanceRuleAlertManager.ts", "lines": 2173, "headerStatus": "v2.1", "priority": "high"}, {"path": "./server/src/platform/governance/reporting-infrastructure/GovernanceRuleReportScheduler.ts", "lines": 2032, "headerStatus": "v2.1", "priority": "high"}, {"path": "./server/src/platform/governance/reporting-infrastructure/GovernanceRuleDashboardGenerator.ts", "lines": 1263, "headerStatus": "minimal-header", "priority": "high"}, {"path": "./server/src/platform/governance/rule-management/RuleDependencyGraphAnalyzer.ts", "lines": 1558, "headerStatus": "v2.0-or-older", "priority": "high"}, {"path": "./server/src/platform/governance/rule-management/RulePerformanceOptimizationEngine.ts", "lines": 1331, "headerStatus": "v2.3", "priority": "high"}, {"path": "./server/src/platform/governance/rule-management/RuleExecutionResultProcessor.ts", "lines": 1750, "headerStatus": "v2.0-or-older", "priority": "high"}, {"path": "./server/src/platform/governance/rule-management/core/GovernanceRuleValidatorFactory.ts", "lines": 954, "headerStatus": "v2.3", "priority": "high"}, {"path": "./server/src/platform/governance/rule-management/core/GovernanceRuleExecutionContext.ts", "lines": 1060, "headerStatus": "v2.0-or-older", "priority": "high"}, {"path": "./server/src/platform/governance/rule-management/core/GovernanceRuleEngineCore.ts", "lines": 1337, "headerStatus": "v2.0-or-older", "priority": "high"}, {"path": "./server/src/platform/governance/rule-management/RuleGovernanceComplianceValidator.ts", "lines": 1388, "headerStatus": "v2.3", "priority": "high"}, {"path": "./server/src/platform/governance/rule-management/compliance/GovernanceComplianceChecker.ts", "lines": 1143, "headerStatus": "v2.0-or-older", "priority": "high"}, {"path": "./server/src/platform/governance/rule-management/compliance/GovernanceAuthorityValidator.ts", "lines": 1573, "headerStatus": "v2.0-or-older", "priority": "high"}, {"path": "./server/src/platform/governance/rule-management/infrastructure/GovernanceRuleCacheManager.ts", "lines": 1190, "headerStatus": "v2.3", "priority": "high"}, {"path": "./server/src/platform/governance/rule-management/infrastructure/GovernanceRuleAuditLogger.ts", "lines": 1480, "headerStatus": "v2.0-or-older", "priority": "high"}, {"path": "./server/src/platform/governance/rule-management/infrastructure/GovernanceRuleMetricsCollector.ts", "lines": 1649, "headerStatus": "v2.3", "priority": "high"}, {"path": "./server/src/platform/governance/rule-management/RuleConflictResolutionEngine.ts", "lines": 1760, "headerStatus": "v2.0-or-older", "priority": "high"}, {"path": "./server/src/platform/governance/rule-management/RuleInheritanceChainManager.ts", "lines": 1790, "headerStatus": "v2.3", "priority": "high"}, {"path": "./server/src/platform/governance/rule-management/RuleExecutionContextManager.ts", "lines": 1546, "headerStatus": "v2.0-or-older", "priority": "high"}, {"path": "./server/src/platform/governance/rule-management/RulePriorityManagementSystem.ts", "lines": 1695, "headerStatus": "v2.3", "priority": "high"}, {"path": "./server/src/platform/governance/continuity-backup/GovernanceRuleRecoveryManager.ts", "lines": 1099, "headerStatus": "v2.1", "priority": "high"}, {"path": "./server/src/platform/governance/continuity-backup/GovernanceRuleDisasterRecovery.ts", "lines": 1387, "headerStatus": "v2.1", "priority": "high"}, {"path": "./server/src/platform/governance/continuity-backup/GovernanceRuleBackupManagerContinuity.ts", "lines": 1480, "headerStatus": "v2.1", "priority": "high"}, {"path": "./server/src/platform/governance/continuity-backup/GovernanceRuleFailoverManager.ts", "lines": 1518, "headerStatus": "v2.1", "priority": "high"}, {"path": "./server/src/platform/governance/enterprise-frameworks/GovernanceRuleEnterpriseFramework.ts", "lines": 1528, "headerStatus": "v2.1", "priority": "high"}, {"path": "./server/src/platform/governance/enterprise-frameworks/GovernanceRuleIntegrationFramework.ts", "lines": 729, "headerStatus": "v2.1", "priority": "high"}, {"path": "./server/src/platform/governance/enterprise-frameworks/GovernanceRuleManagementFramework.ts", "lines": 1122, "headerStatus": "v2.1", "priority": "high"}, {"path": "./server/src/platform/governance/enterprise-frameworks/GovernanceRuleGovernanceFramework.ts", "lines": 1490, "headerStatus": "v2.1", "priority": "high"}, {"path": "./server/src/platform/governance/automation-processing/GovernanceRuleNotificationSystemAutomation.ts", "lines": 1216, "headerStatus": "v2.1", "priority": "high"}, {"path": "./server/src/platform/governance/automation-processing/GovernanceRuleTransformationEngine.ts", "lines": 1529, "headerStatus": "v2.1", "priority": "high"}, {"path": "./server/src/platform/governance/automation-processing/GovernanceRuleMaintenanceScheduler.ts", "lines": 1081, "headerStatus": "v2.1", "priority": "high"}, {"path": "./server/src/platform/governance/automation-processing/GovernanceRuleEventManager.ts", "lines": 1395, "headerStatus": "v2.1", "priority": "high"}, {"path": "./server/src/platform/governance/automation-engines/governance-rule-scheduling-engine.ts", "lines": 901, "headerStatus": "v2.1", "priority": "high"}, {"path": "./server/src/platform/governance/automation-engines/governance-rule-workflow-engine.ts", "lines": 777, "headerStatus": "v2.1", "priority": "high"}, {"path": "./server/src/platform/governance/automation-engines/governance-rule-automation-engine.ts", "lines": 810, "headerStatus": "v2.1", "priority": "high"}, {"path": "./server/src/platform/governance/automation-engines/governance-rule-processing-engine.ts", "lines": 954, "headerStatus": "v2.1", "priority": "high"}, {"path": "./server/src/platform/governance/compliance-infrastructure/GovernanceRuleComplianceChecker.ts", "lines": 1110, "headerStatus": "v2.1", "priority": "high"}, {"path": "./server/src/platform/governance/compliance-infrastructure/GovernanceRuleComplianceFramework.ts", "lines": 1161, "headerStatus": "v2.1", "priority": "high"}, {"path": "./server/src/platform/governance/compliance-infrastructure/GovernanceRuleTestingFramework.ts", "lines": 1059, "headerStatus": "minimal-header", "priority": "high"}, {"path": "./server/src/platform/governance/compliance-infrastructure/GovernanceRuleQualityFramework.ts", "lines": 1191, "headerStatus": "v2.1", "priority": "high"}, {"path": "./server/src/platform/governance/performance-management/analytics/RulePerformanceProfiler.ts", "lines": 926, "headerStatus": "v2.1", "priority": "high"}, {"path": "./server/src/platform/governance/performance-management/monitoring/RuleNotificationSystem.ts", "lines": 930, "headerStatus": "v2.1", "priority": "high"}, {"path": "./server/src/platform/governance/performance-management/monitoring/RuleHealthChecker.ts", "lines": 1108, "headerStatus": "v2.1", "priority": "high"}, {"path": "./server/src/platform/governance/performance-management/monitoring/RuleMonitoringSystem.ts", "lines": 1039, "headerStatus": "v2.1", "priority": "high"}, {"path": "./server/src/platform/governance/performance-management/monitoring/RuleMetricsCollector.ts", "lines": 1183, "headerStatus": "v2.1", "priority": "high"}, {"path": "./server/src/platform/governance/performance-management/optimization/RulePerformanceOptimizer.ts", "lines": 1324, "headerStatus": "v2.1", "priority": "high"}, {"path": "./server/src/platform/governance/performance-management/cache/RuleResourceManager.ts", "lines": 1707, "headerStatus": "v2.1", "priority": "high"}, {"path": "./server/src/platform/governance/performance-management/cache/RuleCacheManager.ts", "lines": 1386, "headerStatus": "v2.1", "priority": "high"}, {"path": "./server/src/platform/documentation/system-docs/GovernanceSystemDocGenerator.ts", "lines": 1426, "headerStatus": "v2.1", "priority": "high"}, {"path": "./server/src/platform/documentation/system-docs/IntegrationDocCompiler.ts", "lines": 1875, "headerStatus": "v2.1", "priority": "high"}, {"path": "./server/src/platform/documentation/system-docs/MemorySafetyDocBuilder.ts", "lines": 1632, "headerStatus": "v2.1", "priority": "high"}, {"path": "./server/src/platform/documentation/system-docs/TroubleshootingGuideAutomation.ts", "lines": 2373, "headerStatus": "minimal-header", "priority": "high"}, {"path": "./server/src/platform/documentation/system-docs/TrackingSystemGuideGenerator.ts", "lines": 2430, "headerStatus": "v2.1", "priority": "high"}, {"path": "./server/src/platform/documentation/training-materials/GovernanceAdminTrainingSystem.ts", "lines": 1573, "headerStatus": "v2.0-or-older", "priority": "high"}, {"path": "./server/src/platform/documentation/training-materials/BestPracticesDocEngine.ts", "lines": 2058, "headerStatus": "v2.1", "priority": "high"}, {"path": "./server/src/platform/documentation/training-materials/CommonWorkflowsGuideGenerator.ts", "lines": 1711, "headerStatus": "minimal-header", "priority": "high"}, {"path": "./server/src/platform/documentation/training-materials/TrackingDashboardTrainingPortal.ts", "lines": 2204, "headerStatus": "minimal-header", "priority": "high"}, {"path": "./server/src/platform/documentation/training-materials/MemorySafetyPracticesGuide.ts", "lines": 1625, "headerStatus": "minimal-header", "priority": "high"}, {"path": "./shared/src/types/platform/governance/management-configuration/environment-manager-types.ts", "lines": 1031, "headerStatus": "v2.1", "priority": "high"}, {"path": "./shared/src/types/platform/governance/management-configuration/tracking-system-guide-generator-types.ts", "lines": 1060, "headerStatus": "v2.1", "priority": "high"}, {"path": "./shared/src/types/platform/governance/management-configuration/documentation-generator-types.ts", "lines": 3805, "headerStatus": "v2.1", "priority": "high"}, {"path": "./shared/src/types/platform/governance/rule-management-types.ts", "lines": 2487, "headerStatus": "v2.1", "priority": "high"}, {"path": "./shared/src/types/platform/governance/automation-processing-types.ts", "lines": 1998, "headerStatus": "v2.1", "priority": "high"}, {"path": "./shared/src/types/platform/governance/governance-interfaces.ts", "lines": 1203, "headerStatus": "v2.1", "priority": "high"}, {"path": "./shared/src/types/platform/governance/automation-engines/workflow-engines-types.ts", "lines": 779, "headerStatus": "v2.1", "priority": "high"}, {"path": "./shared/src/types/platform/governance/governance-types.ts", "lines": 1287, "headerStatus": "v2.1", "priority": "high"}, {"path": "./shared/src/base/EventHandlerRegistryEnhanced.ts", "lines": 1028, "headerStatus": "v2.3", "priority": "medium"}, {"path": "./shared/src/base/TimerCoordinationService.ts", "lines": 713, "headerStatus": "v2.0-or-older", "priority": "medium"}, {"path": "./shared/src/base/event-handler-registry/modules/EventBuffering.ts", "lines": 857, "headerStatus": "v2.1", "priority": "critical"}, {"path": "./shared/src/base/MemorySafeResourceManager.ts", "lines": 1021, "headerStatus": "v2.0-or-older", "priority": "medium"}, {"path": "./shared/src/base/utils/EnterpriseErrorHandling.ts", "lines": 968, "headerStatus": "v2.3", "priority": "critical"}, {"path": "./shared/src/base/AtomicCircularBufferEnhanced.ts", "lines": 1084, "headerStatus": "v2.3", "priority": "medium"}, {"path": "./shared/src/base/MemorySafeResourceManagerEnhanced.ts", "lines": 1278, "headerStatus": "v2.3", "priority": "medium"}, {"path": "./shared/src/base/timer-coordination/modules/TimerCoordinationPatterns.ts", "lines": 796, "headerStatus": "v2.1", "priority": "critical"}, {"path": "./shared/src/base/timer-coordination/modules/AdvancedScheduler.ts", "lines": 719, "headerStatus": "v2.1", "priority": "critical"}, {"path": "./shared/src/base/MemorySafetyManagerEnhanced.ts", "lines": 828, "headerStatus": "v2.3", "priority": "medium"}, {"path": "./shared/src/base/MemorySafetyManager.ts", "lines": 964, "headerStatus": "v2.0-or-older", "priority": "medium"}, {"path": "./shared/src/base/CleanupCoordinatorEnhanced.ts", "lines": 1211, "headerStatus": "v2.0-or-older", "priority": "medium"}, {"path": "./shared/src/base/cleanup-coordinator-enhanced/modules/SystemOrchestrator.ts", "lines": 733, "headerStatus": "v2.1", "priority": "critical"}, {"path": "./shared/src/base/cleanup-coordinator-enhanced/modules/DependencyResolver.ts", "lines": 746, "headerStatus": "v2.1", "priority": "critical"}, {"path": "./shared/src/base/cleanup-coordinator-enhanced/modules/TemplateValidation.ts", "lines": 967, "headerStatus": "v2.1", "priority": "critical"}, {"path": "./shared/src/base/cleanup-coordinator-enhanced/modules/TemplateWorkflows.ts", "lines": 1058, "headerStatus": "v2.1", "priority": "critical"}, {"path": "./shared/src/base/cleanup-coordinator-enhanced/modules/RollbackManager.ts", "lines": 848, "headerStatus": "v2.1", "priority": "critical"}, {"path": "./shared/src/base/cleanup-coordinator-enhanced/modules/OperationExecutionManager.ts", "lines": 794, "headerStatus": "v2.1", "priority": "critical"}, {"path": "./shared/src/interfaces/governance/management-configuration/governance-rule-documentation-generator.ts", "lines": 3737, "headerStatus": "v2.1", "priority": "medium"}, {"path": "./shared/src/interfaces/governance/management-configuration/tracking-system-guide-generator.ts", "lines": 1289, "headerStatus": "v2.1", "priority": "medium"}, {"path": "./shared/src/constants/platform/tracking/environment-constants-calculator.ts", "lines": 735, "headerStatus": "v2.1", "priority": "medium"}, {"path": "./shared/src/constants/platform/tracking/tracking-constants-enhanced.ts", "lines": 734, "headerStatus": "v2.1", "priority": "medium"}, {"path": "./docs/es6-oa-standard-imp-plan.md", "lines": 999, "headerStatus": "no-header", "priority": "low"}, {"path": "./docs/dir-org-prompt.md", "lines": 976, "headerStatus": "no-header", "priority": "low"}, {"path": "./docs/plan/milestone-m11a-business-application-registry.md", "lines": 1908, "headerStatus": "no-header", "priority": "low"}, {"path": "./docs/plan/milestone-06-plugin-system.md", "lines": 1003, "headerStatus": "no-header", "priority": "low"}, {"path": "./docs/plan/milestone-08-advanced-governance-consolidated.md", "lines": 1509, "headerStatus": "v2.0-or-older", "priority": "low"}, {"path": "./docs/plan/Milestone-11-external-database-management.md", "lines": 2573, "headerStatus": "v2.0-or-older", "priority": "low"}, {"path": "./docs/plan/implemented/milestone-00-enhancements-m0.1.md", "lines": 997, "headerStatus": "no-header", "priority": "low"}, {"path": "./docs/plan/milestone-07-production-ready.md", "lines": 2129, "headerStatus": "no-header", "priority": "low"}, {"path": "./docs/plan/milestone-02-governance-integrated.md", "lines": 1112, "headerStatus": "no-header", "priority": "low"}, {"path": "./docs/plan/milestone-04a-administration-interface.md", "lines": 1639, "headerStatus": "no-header", "priority": "low"}, {"path": "./docs/plan/milestone-01b-bootstrap.md", "lines": 1464, "headerStatus": "v2.0-or-older", "priority": "low"}, {"path": "./docs/plan/milestone-01c-business-application-foundation.md", "lines": 1410, "headerStatus": "no-header", "priority": "low"}, {"path": "./docs/plan/milestones-ndx-enh.md", "lines": 945, "headerStatus": "no-header", "priority": "low"}, {"path": "./docs/plan/milestone-01a-foundation-for-m11.md", "lines": 1164, "headerStatus": "v2.0-or-older", "priority": "low"}, {"path": "./docs/plan/milestone-03-user-dashboard.md", "lines": 1098, "headerStatus": "v2.0-or-older", "priority": "low"}, {"path": "./docs/plan/milestone-07a-foundation-for-m11.md", "lines": 1505, "headerStatus": "no-header", "priority": "low"}, {"path": "./docs/plan/milestone-m6.1-universal-standards-cartridge-system.md", "lines": 2854, "headerStatus": "no-header", "priority": "low"}, {"path": "./docs/plan/milestone-07b-framework-enterprise-infra.md", "lines": 1453, "headerStatus": "v2.0-or-older", "priority": "low"}, {"path": "./docs/plan/milestone-m11b-resource-inheritance-framework.md", "lines": 2007, "headerStatus": "no-header", "priority": "low"}, {"path": "./docs/plan/milestone-02a-application-authentication.md", "lines": 1819, "headerStatus": "v2.0-or-older", "priority": "low"}, {"path": "./docs/plan/milestone-04-admin-panel.md", "lines": 937, "headerStatus": "no-header", "priority": "low"}, {"path": "./docs/plan/milestone-00-governance-tracking.md", "lines": 2216, "headerStatus": "no-header", "priority": "low"}, {"path": "./docs/plan/milestone-00-enhancements-m0.1.md", "lines": 1225, "headerStatus": "no-header", "priority": "low"}, {"path": "./docs/plan/milestone-01-governance-first.md", "lines": 958, "headerStatus": "v2.0-or-older", "priority": "low"}, {"path": "./docs/processes/development-workflow.md", "lines": 1276, "headerStatus": "no-header", "priority": "low"}, {"path": "./docs/processes/ai-command-reference.md", "lines": 742, "headerStatus": "no-header", "priority": "low"}, {"path": "./docs/tracking/tracking-system.md", "lines": 1510, "headerStatus": "no-header", "priority": "low"}, {"path": "./docs/tracking/logging-system.md", "lines": 1146, "headerStatus": "no-header", "priority": "low"}, {"path": "./docs/demos-prompts/m0-demo/m0-demo-prompt.md", "lines": 784, "headerStatus": "no-header", "priority": "low"}, {"path": "./docs/demos-prompts/m0-demo/m0-demo-implementation-plan.md", "lines": 1056, "headerStatus": "no-header", "priority": "low"}, {"path": "./docs/demos-prompts/m0-real-demo/m0-prompt-for-plan.md", "lines": 1541, "headerStatus": "no-header", "priority": "low"}, {"path": "./docs/core/template-system.md", "lines": 1119, "headerStatus": "minimal-header", "priority": "low"}, {"path": "./docs/core/development-standards-v21.md", "lines": 994, "headerStatus": "no-header", "priority": "low"}, {"path": "./docs/core/governance-process.md", "lines": 911, "headerStatus": "no-header", "priority": "low"}, {"path": "./docs/core/automatic-universal-governance-driver-v7.1.md", "lines": 1068, "headerStatus": "v2.0-or-older", "priority": "low"}, {"path": "./docs/core/orchestration-driver.md", "lines": 1947, "headerStatus": "no-header", "priority": "low"}, {"path": "./docs/core/session-management.md", "lines": 953, "headerStatus": "no-header", "priority": "low"}, {"path": "./docs/ai/ai-instructions.md", "lines": 900, "headerStatus": "no-header", "priority": "low"}, {"path": "./docs/contexts/foundation-context/guides/performance-optimization.md", "lines": 857, "headerStatus": "v2.1", "priority": "low"}, {"path": "./docs/contexts/foundation-context/guides/M0-COMPONENT-TESTING-PLAN.md", "lines": 1083, "headerStatus": "no-header", "priority": "low"}, {"path": "./docs/contexts/foundation-context/services/governance-tracking-bridge.md", "lines": 815, "headerStatus": "no-header", "priority": "low"}, {"path": "./docs/contexts/foundation-context/services/realtime-event-coordinator.md", "lines": 990, "headerStatus": "no-header", "priority": "low"}, {"path": "./docs/contexts/foundation-context/services/authority-compliance-monitor-bridge.md", "lines": 1121, "headerStatus": "no-header", "priority": "low"}, {"path": "./docs/contexts/foundation-context/services/memory-safety-integration-validator.md", "lines": 966, "headerStatus": "no-header", "priority": "low"}, {"path": "./docs/contexts/foundation-context/services/cross-reference-validation-bridge.md", "lines": 1095, "headerStatus": "no-header", "priority": "low"}, {"path": "./docs/governance/contexts/foundation-context/03-dcr/DCR-foundation-005-g-tsk-06-performance-standards.md", "lines": 730, "headerStatus": "no-header", "priority": "low"}, {"path": "./docs/issues-with-x-models.md", "lines": 1439, "headerStatus": "no-header", "priority": "low"}, {"path": "./docs/templates/typescript-header-templates.md", "lines": 823, "headerStatus": "v2.3", "priority": "low"}, {"path": "./docs/templates/security-validation-templates.md", "lines": 761, "headerStatus": "minimal-header", "priority": "low"}, {"path": "./docs/templates/ai-collaboration-templates.md", "lines": 841, "headerStatus": "minimal-header", "priority": "low"}, {"path": "./docs/lessons/lesson-learned-07-EventHandlerRegistry.md", "lines": 860, "headerStatus": "no-header", "priority": "low"}, {"path": "./docs/lessons/lesson-learned-06-MemorySafetyManager.md", "lines": 802, "headerStatus": "no-header", "priority": "low"}, {"path": "./docs/lessons/lesson-learned-10-CleanupCoordinatorEnhanced.md", "lines": 1123, "headerStatus": "no-header", "priority": "low"}, {"path": "./docs/lessons/lesson-learned-08-TimerCoordinationService.md", "lines": 825, "headerStatus": "no-header", "priority": "low"}, {"path": "./docs/lessons/quick-reference.md", "lines": 736, "headerStatus": "minimal-header", "priority": "low"}, {"path": "./docs/refactors/refactoring-plan-2025-06-23.md", "lines": 848, "headerStatus": "no-header", "priority": "low"}], "needsUpgrade": [{"path": "./server/src/platform/integration/core-bridge/RealtimeEventCoordinator.ts", "headerStatus": "minimal-header", "lines": 2657, "size": 76788, "tier": "server", "category": "typescript", "priority": "critical"}, {"path": "./server/src/platform/integration/core-bridge/AuthorityComplianceMonitorBridge.ts", "headerStatus": "minimal-header", "lines": 2324, "size": 76330, "tier": "server", "category": "typescript", "priority": "critical"}, {"path": "./server/src/platform/integration/core-bridge/CrossReferenceValidationBridge.ts", "headerStatus": "v2.0-or-older", "lines": 1877, "size": 62207, "tier": "server", "category": "typescript", "priority": "critical"}, {"path": "./server/src/platform/integration/core-bridge/GovernanceTrackingBridge.ts", "headerStatus": "v2.1", "lines": 3087, "size": 98108, "tier": "server", "category": "typescript", "priority": "critical"}, {"path": "./server/src/platform/tracking/advanced-data/SmartPathResolutionSystem.ts", "headerStatus": "v2.1", "lines": 924, "size": 30715, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/tracking/advanced-data/ContextAuthorityProtocol.ts", "headerStatus": "v2.1", "lines": 1431, "size": 47746, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/tracking/advanced-data/CrossReferenceValidationEngine.ts", "headerStatus": "v2.1", "lines": 1259, "size": 40200, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/tracking/advanced-data/OrchestrationCoordinator.ts", "headerStatus": "v2.1", "lines": 1280, "size": 41206, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/tracking/core-data/GovernanceLogTracker.ts", "headerStatus": "v2.0-or-older", "lines": 1847, "size": 62645, "tier": "server", "category": "typescript", "priority": "critical"}, {"path": "./server/src/platform/tracking/core-data/ImplementationProgressTracker.ts", "headerStatus": "v2.0-or-older", "lines": 1484, "size": 51532, "tier": "server", "category": "typescript", "priority": "critical"}, {"path": "./server/src/platform/tracking/core-data/base/BaseTrackingService.ts", "headerStatus": "v2.0-or-older", "lines": 1977, "size": 65416, "tier": "server", "category": "typescript", "priority": "critical"}, {"path": "./server/src/platform/tracking/core-data/AnalyticsCacheManager.ts", "headerStatus": "v2.0-or-older", "lines": 2030, "size": 64684, "tier": "server", "category": "typescript", "priority": "critical"}, {"path": "./server/src/platform/tracking/core-data/SessionLogTracker.ts", "headerStatus": "v2.0-or-older", "lines": 2818, "size": 91291, "tier": "server", "category": "typescript", "priority": "critical"}, {"path": "./server/src/platform/tracking/core-utils/TrackingUtilities.ts", "headerStatus": "v2.1", "lines": 1031, "size": 32553, "tier": "server", "category": "typescript", "priority": "critical"}, {"path": "./server/src/platform/tracking/core-managers/TrackingManager.ts", "headerStatus": "v2.1", "lines": 1082, "size": 36717, "tier": "server", "category": "typescript", "priority": "critical"}, {"path": "./server/src/platform/tracking/core-managers/DashboardManager.ts", "headerStatus": "v2.1", "lines": 1332, "size": 43416, "tier": "server", "category": "typescript", "priority": "critical"}, {"path": "./server/src/platform/tracking/core-managers/FileManager.ts", "headerStatus": "v2.1", "lines": 880, "size": 26085, "tier": "server", "category": "typescript", "priority": "critical"}, {"path": "./server/src/platform/tracking/core-managers/RealTimeManager.ts", "headerStatus": "v2.1", "lines": 1735, "size": 58807, "tier": "server", "category": "typescript", "priority": "critical"}, {"path": "./server/src/platform/tracking/core-trackers/AuthorityTrackingService.ts", "headerStatus": "v2.1", "lines": 465, "size": 15801, "tier": "server", "category": "typescript", "priority": "critical"}, {"path": "./server/src/platform/tracking/core-trackers/SessionTrackingCore.ts", "headerStatus": "v2.1", "lines": 735, "size": 22445, "tier": "server", "category": "typescript", "priority": "critical"}, {"path": "./server/src/platform/tracking/core-trackers/SessionTrackingUtils.ts", "headerStatus": "v2.1", "lines": 533, "size": 16626, "tier": "server", "category": "typescript", "priority": "critical"}, {"path": "./server/src/platform/tracking/core-trackers/security/ISecurityEnforcement.ts", "headerStatus": "minimal-header", "lines": 142, "size": 3581, "tier": "server", "category": "typescript", "priority": "critical"}, {"path": "./server/src/platform/tracking/core-trackers/security/SecurityConfig.ts", "headerStatus": "minimal-header", "lines": 238, "size": 5446, "tier": "server", "category": "typescript", "priority": "critical"}, {"path": "./server/src/platform/tracking/core-trackers/security/SecurityEnforcementLayer.ts", "headerStatus": "minimal-header", "lines": 364, "size": 12045, "tier": "server", "category": "typescript", "priority": "critical"}, {"path": "./server/src/platform/tracking/core-trackers/CrossReferenceTrackingEngine.ts", "headerStatus": "v2.1", "lines": 381, "size": 13058, "tier": "server", "category": "typescript", "priority": "critical"}, {"path": "./server/src/platform/tracking/core-trackers/SessionTrackingAudit.ts", "headerStatus": "v2.1", "lines": 554, "size": 17375, "tier": "server", "category": "typescript", "priority": "critical"}, {"path": "./server/src/platform/tracking/core-trackers/OrchestrationTrackingSystem.ts", "headerStatus": "v2.1", "lines": 457, "size": 16915, "tier": "server", "category": "typescript", "priority": "critical"}, {"path": "./server/src/platform/tracking/core-trackers/ProgressTrackingEngine.ts", "headerStatus": "v2.1", "lines": 978, "size": 30660, "tier": "server", "category": "typescript", "priority": "critical"}, {"path": "./server/src/platform/tracking/core-trackers/SessionTrackingRealtime.ts", "headerStatus": "v2.1", "lines": 386, "size": 11509, "tier": "server", "category": "typescript", "priority": "critical"}, {"path": "./server/src/platform/tracking/core-trackers/SmartPathTrackingSystem.ts", "headerStatus": "v2.1", "lines": 375, "size": 11193, "tier": "server", "category": "typescript", "priority": "critical"}, {"path": "./server/src/platform/governance/analytics-engines/GovernanceRuleReportingEngineFactory.ts", "headerStatus": "minimal-header", "lines": 634, "size": 19418, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/analytics-engines/GovernanceRuleInsightsGeneratorFactory.ts", "headerStatus": "v2.1", "lines": 687, "size": 22491, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/analytics-engines/GovernanceRuleAnalyticsEngineFactory.ts", "headerStatus": "v2.1", "lines": 594, "size": 17734, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/analytics-engines/GovernanceRuleAnalyticsEngine.ts", "headerStatus": "v2.1", "lines": 1434, "size": 46914, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/analytics-engines/index.ts", "headerStatus": "v2.1", "lines": 275, "size": 8825, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/analytics-engines/GovernanceRuleOptimizationEngineFactory.ts", "headerStatus": "v2.1", "lines": 603, "size": 19503, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/analytics-engines/GovernanceRuleReportingEngine.ts", "headerStatus": "minimal-header", "lines": 1174, "size": 38006, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/analytics-engines/GovernanceRuleInsightsGenerator.ts", "headerStatus": "v2.1", "lines": 1074, "size": 31802, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/analytics-engines/GovernanceRuleOptimizationEngine.ts", "headerStatus": "v2.1", "lines": 1578, "size": 51677, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/security-management/RuleSecurityManager.ts", "headerStatus": "v2.1", "lines": 807, "size": 25336, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/security-management/RuleAuditLogger.ts", "headerStatus": "v2.1", "lines": 654, "size": 20507, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/security-management/RuleSecurityFramework.ts", "headerStatus": "v2.1", "lines": 738, "size": 23720, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/security-management/RuleIntegrityValidator.ts", "headerStatus": "v2.1", "lines": 719, "size": 23164, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/management-configuration/GovernanceRuleTemplateEngine.ts", "headerStatus": "v2.1", "lines": 1627, "size": 50352, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/management-configuration/GovernanceRuleSecurityPolicy.ts", "headerStatus": "v2.1", "lines": 536, "size": 17116, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/management-configuration/GovernanceRuleConfigurationManager.ts", "headerStatus": "v2.1", "lines": 1169, "size": 35912, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/management-configuration/GovernanceRuleDocumentationGenerator.ts", "headerStatus": "v2.1", "lines": 1222, "size": 42587, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/management-configuration/GovernanceRuleInputValidator.ts", "headerStatus": "v2.1", "lines": 1201, "size": 40591, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/management-configuration/GovernanceRuleTemplateSecurity.ts", "headerStatus": "v2.1", "lines": 823, "size": 26276, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/management-configuration/GovernanceRuleEnvironmentManager.ts", "headerStatus": "v2.1", "lines": 1172, "size": 38544, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/reporting-infrastructure/GovernanceRuleComplianceReporter.ts", "headerStatus": "v2.1", "lines": 1773, "size": 58874, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/reporting-infrastructure/GovernanceRuleDashboardGeneratorFactory.ts", "headerStatus": "minimal-header", "lines": 357, "size": 12258, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/reporting-infrastructure/GovernanceRuleAlertManager.ts", "headerStatus": "v2.1", "lines": 2173, "size": 71900, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/reporting-infrastructure/index.ts", "headerStatus": "minimal-header", "lines": 198, "size": 6159, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/reporting-infrastructure/GovernanceRuleComplianceReporterFactory.ts", "headerStatus": "v2.1", "lines": 654, "size": 21756, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/reporting-infrastructure/GovernanceRuleReportScheduler.ts", "headerStatus": "v2.1", "lines": 2032, "size": 67335, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/reporting-infrastructure/GovernanceRuleDashboardGenerator.ts", "headerStatus": "minimal-header", "lines": 1263, "size": 39902, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/reporting-infrastructure/GovernanceRuleReportSchedulerFactory.ts", "headerStatus": "v2.1", "lines": 684, "size": 23120, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/reporting-infrastructure/GovernanceRuleAlertManagerFactory.ts", "headerStatus": "v2.1", "lines": 400, "size": 12981, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/rule-management/RuleDependencyGraphAnalyzer.ts", "headerStatus": "v2.0-or-older", "lines": 1558, "size": 46442, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/rule-management/RuleExecutionResultProcessor.ts", "headerStatus": "v2.0-or-older", "lines": 1750, "size": 52617, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/rule-management/core/GovernanceRuleExecutionContext.ts", "headerStatus": "v2.0-or-older", "lines": 1060, "size": 33594, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/rule-management/core/GovernanceRuleEngineCore.ts", "headerStatus": "v2.0-or-older", "lines": 1337, "size": 41984, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/rule-management/compliance/GovernanceComplianceChecker.ts", "headerStatus": "v2.0-or-older", "lines": 1143, "size": 36617, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/rule-management/compliance/GovernanceAuthorityValidator.ts", "headerStatus": "v2.0-or-older", "lines": 1573, "size": 51182, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/rule-management/infrastructure/GovernanceRuleAuditLogger.ts", "headerStatus": "v2.0-or-older", "lines": 1480, "size": 47162, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/rule-management/RuleConflictResolutionEngine.ts", "headerStatus": "v2.0-or-older", "lines": 1760, "size": 54213, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/rule-management/RuleExecutionContextManager.ts", "headerStatus": "v2.0-or-older", "lines": 1546, "size": 46905, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/continuity-backup/GovernanceRuleRecoveryManager.ts", "headerStatus": "v2.1", "lines": 1099, "size": 33116, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/continuity-backup/GovernanceRuleDisasterRecovery.ts", "headerStatus": "v2.1", "lines": 1387, "size": 41589, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/continuity-backup/GovernanceRuleBackupManagerContinuity.ts", "headerStatus": "v2.1", "lines": 1480, "size": 43120, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/continuity-backup/GovernanceRuleFailoverManager.ts", "headerStatus": "v2.1", "lines": 1518, "size": 44559, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/enterprise-frameworks/GovernanceRuleEnterpriseFramework.ts", "headerStatus": "v2.1", "lines": 1528, "size": 42373, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/enterprise-frameworks/GovernanceRuleIntegrationFramework.ts", "headerStatus": "v2.1", "lines": 729, "size": 24361, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/enterprise-frameworks/GovernanceRuleManagementFramework.ts", "headerStatus": "v2.1", "lines": 1122, "size": 34044, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/enterprise-frameworks/GovernanceRuleGovernanceFramework.ts", "headerStatus": "v2.1", "lines": 1490, "size": 42905, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/automation-processing/GovernanceRuleNotificationSystemAutomation.ts", "headerStatus": "v2.1", "lines": 1216, "size": 41818, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/automation-processing/factories/RuleAuditLoggerFactory.ts", "headerStatus": "v2.1", "lines": 146, "size": 5944, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/automation-processing/GovernanceRuleTransformationEngine.ts", "headerStatus": "v2.1", "lines": 1529, "size": 50859, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/automation-processing/errors/ProcessingErrors.ts", "headerStatus": "v2.1", "lines": 265, "size": 9694, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/automation-processing/GovernanceRuleMaintenanceScheduler.ts", "headerStatus": "v2.1", "lines": 1081, "size": 36343, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/automation-processing/GovernanceRuleEventManager.ts", "headerStatus": "v2.1", "lines": 1395, "size": 46019, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/automation-engines/governance-rule-scheduling-engine.ts", "headerStatus": "v2.1", "lines": 901, "size": 30398, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/automation-engines/governance-rule-workflow-engine.ts", "headerStatus": "v2.1", "lines": 777, "size": 24484, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/automation-engines/governance-rule-automation-engine.ts", "headerStatus": "v2.1", "lines": 810, "size": 27379, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/automation-engines/governance-rule-processing-engine.ts", "headerStatus": "v2.1", "lines": 954, "size": 32664, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/compliance-infrastructure/GovernanceRuleComplianceChecker.ts", "headerStatus": "v2.1", "lines": 1110, "size": 34744, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/compliance-infrastructure/GovernanceRuleComplianceFramework.ts", "headerStatus": "v2.1", "lines": 1161, "size": 35362, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/compliance-infrastructure/GovernanceRuleTestingFramework.ts", "headerStatus": "minimal-header", "lines": 1059, "size": 35306, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/compliance-infrastructure/GovernanceRuleQualityFramework.ts", "headerStatus": "v2.1", "lines": 1191, "size": 37246, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/compliance-infrastructure/index.ts", "headerStatus": "v2.1", "lines": 99, "size": 3515, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/performance-management/analytics/RulePerformanceProfiler.ts", "headerStatus": "v2.1", "lines": 926, "size": 28776, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/performance-management/monitoring/RuleNotificationSystem.ts", "headerStatus": "v2.1", "lines": 930, "size": 30555, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/performance-management/monitoring/RuleHealthChecker.ts", "headerStatus": "v2.1", "lines": 1108, "size": 34937, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/performance-management/monitoring/RuleMonitoringSystem.ts", "headerStatus": "v2.1", "lines": 1039, "size": 32125, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/performance-management/monitoring/RuleMetricsCollector.ts", "headerStatus": "v2.1", "lines": 1183, "size": 40290, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/performance-management/optimization/RulePerformanceOptimizer.ts", "headerStatus": "v2.1", "lines": 1324, "size": 40747, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/performance-management/cache/RuleResourceManager.ts", "headerStatus": "v2.1", "lines": 1707, "size": 53217, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/performance-management/cache/RuleCacheManager.ts", "headerStatus": "v2.1", "lines": 1386, "size": 43735, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/documentation/system-docs/GovernanceSystemDocGenerator.ts", "headerStatus": "v2.1", "lines": 1426, "size": 49518, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/documentation/system-docs/IntegrationDocCompiler.ts", "headerStatus": "v2.1", "lines": 1875, "size": 65213, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/documentation/system-docs/MemorySafetyDocBuilder.ts", "headerStatus": "v2.1", "lines": 1632, "size": 58854, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/documentation/system-docs/TroubleshootingGuideAutomation.ts", "headerStatus": "minimal-header", "lines": 2373, "size": 79689, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/documentation/system-docs/TrackingSystemGuideGenerator.ts", "headerStatus": "v2.1", "lines": 2430, "size": 79517, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/documentation/training-materials/GovernanceAdminTrainingSystem.ts", "headerStatus": "v2.0-or-older", "lines": 1573, "size": 54833, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/documentation/training-materials/BestPracticesDocEngine.ts", "headerStatus": "v2.1", "lines": 2058, "size": 67312, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/documentation/training-materials/CommonWorkflowsGuideGenerator.ts", "headerStatus": "minimal-header", "lines": 1711, "size": 56969, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/documentation/training-materials/TrackingDashboardTrainingPortal.ts", "headerStatus": "minimal-header", "lines": 2204, "size": 77286, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/documentation/training-materials/MemorySafetyPracticesGuide.ts", "headerStatus": "minimal-header", "lines": 1625, "size": 57090, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/types/dependency-types.ts", "headerStatus": "v2.1", "lines": 61, "size": 2107, "tier": "server", "category": "typescript", "priority": "medium"}, {"path": "./server/src/errors/security-errors.ts", "headerStatus": "v2.1", "lines": 78, "size": 2574, "tier": "server", "category": "typescript", "priority": "medium"}, {"path": "./server/src/interfaces/storage/storage-interfaces.ts", "headerStatus": "v2.1", "lines": 103, "size": 3072, "tier": "server", "category": "typescript", "priority": "medium"}, {"path": "./server/src/interfaces/configuration/configuration-interfaces.ts", "headerStatus": "v2.1", "lines": 72, "size": 2310, "tier": "server", "category": "typescript", "priority": "medium"}, {"path": "./server/src/interfaces/security/security-interfaces.ts", "headerStatus": "v2.1", "lines": 138, "size": 3873, "tier": "server", "category": "typescript", "priority": "medium"}, {"path": "./server/src/interfaces/security/crypto-interfaces.ts", "headerStatus": "v2.1", "lines": 81, "size": 2408, "tier": "server", "category": "typescript", "priority": "medium"}, {"path": "./server/src/interfaces/security/audit-interfaces.ts", "headerStatus": "v2.1", "lines": 181, "size": 4607, "tier": "server", "category": "typescript", "priority": "medium"}, {"path": "./server/src/interfaces/security/hash-interfaces.ts", "headerStatus": "v2.1", "lines": 75, "size": 2182, "tier": "server", "category": "typescript", "priority": "medium"}, {"path": "./server/src/interfaces/security/integrity-interfaces.ts", "headerStatus": "v2.1", "lines": 161, "size": 4445, "tier": "server", "category": "typescript", "priority": "medium"}, {"path": "./server/src/interfaces/security/authorization-interfaces.ts", "headerStatus": "v2.1", "lines": 69, "size": 2135, "tier": "server", "category": "typescript", "priority": "medium"}, {"path": "./server/src/interfaces/security/framework-interfaces.ts", "headerStatus": "v2.1", "lines": 116, "size": 3455, "tier": "server", "category": "typescript", "priority": "medium"}, {"path": "./server/src/interfaces/logging/logging-interfaces.ts", "headerStatus": "v2.1", "lines": 89, "size": 2505, "tier": "server", "category": "typescript", "priority": "medium"}, {"path": "./server/src/interfaces/monitoring/monitoring-interfaces.ts", "headerStatus": "v2.1", "lines": 93, "size": 2601, "tier": "server", "category": "typescript", "priority": "medium"}, {"path": "./shared/src/types/platform/tracking/tracking-types.ts", "headerStatus": "v2.1", "lines": 113, "size": 4320, "tier": "shared", "category": "typescript", "priority": "high"}, {"path": "./shared/src/types/platform/tracking/utilities/workflow-types.ts", "headerStatus": "v2.1", "lines": 92, "size": 2857, "tier": "shared", "category": "typescript", "priority": "high"}, {"path": "./shared/src/types/platform/tracking/utilities/metrics-types.ts", "headerStatus": "v2.1", "lines": 81, "size": 2505, "tier": "shared", "category": "typescript", "priority": "high"}, {"path": "./shared/src/types/platform/tracking/utilities/error-types.ts", "headerStatus": "v2.1", "lines": 91, "size": 2772, "tier": "shared", "category": "typescript", "priority": "high"}, {"path": "./shared/src/types/platform/tracking/core/base-types.ts", "headerStatus": "v2.1", "lines": 276, "size": 6303, "tier": "shared", "category": "typescript", "priority": "high"}, {"path": "./shared/src/types/platform/tracking/core/tracking-data-types.ts", "headerStatus": "v2.1", "lines": 511, "size": 11953, "tier": "shared", "category": "typescript", "priority": "high"}, {"path": "./shared/src/types/platform/tracking/core/tracking-service-types.ts", "headerStatus": "v2.1", "lines": 338, "size": 8975, "tier": "shared", "category": "typescript", "priority": "high"}, {"path": "./shared/src/types/platform/tracking/core/tracking-config-types.ts", "headerStatus": "v2.1", "lines": 275, "size": 6821, "tier": "shared", "category": "typescript", "priority": "high"}, {"path": "./shared/src/types/platform/tracking/index.ts", "headerStatus": "v2.1", "lines": 273, "size": 8063, "tier": "shared", "category": "typescript", "priority": "high"}, {"path": "./shared/src/types/platform/governance/notification-types.ts", "headerStatus": "v2.1", "lines": 151, "size": 4272, "tier": "shared", "category": "typescript", "priority": "high"}, {"path": "./shared/src/types/platform/governance/management-configuration/environment-manager-types.ts", "headerStatus": "v2.1", "lines": 1031, "size": 22371, "tier": "shared", "category": "typescript", "priority": "high"}, {"path": "./shared/src/types/platform/governance/management-configuration/tracking-system-guide-generator-types.ts", "headerStatus": "v2.1", "lines": 1060, "size": 24035, "tier": "shared", "category": "typescript", "priority": "high"}, {"path": "./shared/src/types/platform/governance/management-configuration/documentation-generator-types.ts", "headerStatus": "v2.1", "lines": 3805, "size": 86651, "tier": "shared", "category": "typescript", "priority": "high"}, {"path": "./shared/src/types/platform/governance/resource-interfaces.ts", "headerStatus": "v2.1", "lines": 111, "size": 3675, "tier": "shared", "category": "typescript", "priority": "high"}, {"path": "./shared/src/types/platform/governance/security-types.ts", "headerStatus": "v2.1", "lines": 381, "size": 10944, "tier": "shared", "category": "typescript", "priority": "high"}, {"path": "./shared/src/types/platform/governance/rule-management-types.ts", "headerStatus": "v2.1", "lines": 2487, "size": 53575, "tier": "shared", "category": "typescript", "priority": "high"}, {"path": "./shared/src/types/platform/governance/automation-processing-types.ts", "headerStatus": "v2.1", "lines": 1998, "size": 56518, "tier": "shared", "category": "typescript", "priority": "high"}, {"path": "./shared/src/types/platform/governance/governance-interfaces.ts", "headerStatus": "v2.1", "lines": 1203, "size": 33081, "tier": "shared", "category": "typescript", "priority": "high"}, {"path": "./shared/src/types/platform/governance/automation-engines/workflow-engines-types.ts", "headerStatus": "v2.1", "lines": 779, "size": 26201, "tier": "shared", "category": "typescript", "priority": "high"}, {"path": "./shared/src/types/platform/governance/governance-types.ts", "headerStatus": "v2.1", "lines": 1287, "size": 30855, "tier": "shared", "category": "typescript", "priority": "high"}, {"path": "./shared/src/types/tracking/tracking-management-types.ts", "headerStatus": "v2.1", "lines": 523, "size": 12540, "tier": "shared", "category": "typescript", "priority": "high"}, {"path": "./shared/src/types/tracking/core-types.ts", "headerStatus": "v2.1", "lines": 248, "size": 6095, "tier": "shared", "category": "typescript", "priority": "high"}, {"path": "./shared/src/base/EventHandlerRegistry.ts", "headerStatus": "v2.0-or-older", "lines": 577, "size": 20827, "tier": "shared", "category": "typescript", "priority": "medium"}, {"path": "./shared/src/base/TimerCoordinationService.ts", "headerStatus": "v2.0-or-older", "lines": 713, "size": 25945, "tier": "shared", "category": "typescript", "priority": "medium"}, {"path": "./shared/src/base/event-handler-registry/types/EventHandlerEnhancedTypes.ts", "headerStatus": "v2.1", "lines": 231, "size": 7675, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/event-handler-registry/types/EventTypes.ts", "headerStatus": "v2.1", "lines": 349, "size": 11508, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/event-handler-registry/types/EventConfiguration.ts", "headerStatus": "v2.1", "lines": 396, "size": 12996, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/event-handler-registry/modules/DeduplicationEngine.ts", "headerStatus": "v2.1", "lines": 587, "size": 21868, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/event-handler-registry/modules/MetricsManager.ts", "headerStatus": "v2.1", "lines": 331, "size": 10980, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/event-handler-registry/modules/ComplianceManager.ts", "headerStatus": "v2.1", "lines": 374, "size": 12567, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/event-handler-registry/modules/MiddlewareManager.ts", "headerStatus": "v2.1", "lines": 457, "size": 15557, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/event-handler-registry/modules/EventBuffering.ts", "headerStatus": "v2.1", "lines": 857, "size": 29239, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/event-handler-registry/modules/EventUtilities.ts", "headerStatus": "v2.1", "lines": 314, "size": 10551, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/event-handler-registry/modules/EventEmissionSystem.ts", "headerStatus": "v2.1", "lines": 385, "size": 13021, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/MemorySafeResourceManager.ts", "headerStatus": "v2.0-or-older", "lines": 1021, "size": 36262, "tier": "shared", "category": "typescript", "priority": "medium"}, {"path": "./shared/src/base/AtomicCircularBuffer.ts", "headerStatus": "v2.0-or-older", "lines": 558, "size": 20009, "tier": "shared", "category": "typescript", "priority": "medium"}, {"path": "./shared/src/base/atomic-circular-buffer-enhanced/modules/BufferUtilities.ts", "headerStatus": "v2.1", "lines": 459, "size": 14787, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/atomic-circular-buffer-enhanced/modules/BufferStrategyManager.ts", "headerStatus": "v2.1", "lines": 652, "size": 22536, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/atomic-circular-buffer-enhanced/modules/BufferConfigurationManager.ts", "headerStatus": "v2.1", "lines": 499, "size": 17485, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/atomic-circular-buffer-enhanced/modules/BufferPersistenceManager.ts", "headerStatus": "v2.1", "lines": 534, "size": 17852, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/atomic-circular-buffer-enhanced/modules/BufferOperationsManager.ts", "headerStatus": "v2.1", "lines": 470, "size": 15682, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/atomic-circular-buffer-enhanced/modules/BufferAnalyticsEngine.ts", "headerStatus": "v2.1", "lines": 633, "size": 22407, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/utils/ResilientTiming.ts", "headerStatus": "v2.0-or-older", "lines": 371, "size": 12655, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/LoggingMixin.ts", "headerStatus": "v2.0-or-older", "lines": 236, "size": 9310, "tier": "shared", "category": "typescript", "priority": "medium"}, {"path": "./shared/src/base/timer-coordination/types/TimerTypes.ts", "headerStatus": "v2.1", "lines": 385, "size": 12499, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/timer-coordination/modules/TimerCoordinationPatterns.ts", "headerStatus": "v2.1", "lines": 796, "size": 28031, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/timer-coordination/modules/TimerConfiguration.ts", "headerStatus": "v2.1", "lines": 358, "size": 11572, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/timer-coordination/modules/PhaseIntegration.ts", "headerStatus": "v2.1", "lines": 407, "size": 15285, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/timer-coordination/modules/TimerUtilities.ts", "headerStatus": "v2.1", "lines": 541, "size": 20205, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/timer-coordination/modules/AdvancedScheduler.ts", "headerStatus": "v2.1", "lines": 719, "size": 25285, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/timer-coordination/modules/TimerPoolManager.ts", "headerStatus": "v2.1", "lines": 584, "size": 20563, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/memory-safety-manager/modules/SystemCoordinationManager.ts", "headerStatus": "v2.1", "lines": 644, "size": 20901, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/memory-safety-manager/modules/SystemStateManager.ts", "headerStatus": "v2.1", "lines": 597, "size": 18305, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/memory-safety-manager/modules/ComponentDiscoveryManager.ts", "headerStatus": "v2.1", "lines": 633, "size": 21203, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/memory-safety-manager/modules/EnhancedMetricsCollector.ts", "headerStatus": "v2.1", "lines": 595, "size": 18629, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/memory-safety-manager/modules/ComponentIntegrationEngine.ts", "headerStatus": "v2.1", "lines": 342, "size": 11604, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/memory-safety-manager/modules/EnhancedConfigurationManager.ts", "headerStatus": "v2.1", "lines": 432, "size": 14713, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/MemorySafetyManager.ts", "headerStatus": "v2.0-or-older", "lines": 964, "size": 33186, "tier": "shared", "category": "typescript", "priority": "medium"}, {"path": "./shared/src/base/CleanupCoordinatorEnhanced.ts", "headerStatus": "v2.0-or-older", "lines": 1211, "size": 41773, "tier": "shared", "category": "typescript", "priority": "medium"}, {"path": "./shared/src/base/cleanup-coordinator-enhanced/modules/CleanupConfiguration.ts", "headerStatus": "v2.1", "lines": 459, "size": 15716, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/cleanup-coordinator-enhanced/modules/RollbackSnapshots.ts", "headerStatus": "v2.1", "lines": 312, "size": 11147, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/cleanup-coordinator-enhanced/modules/CleanupTemplateManager.ts", "headerStatus": "v2.1", "lines": 677, "size": 24545, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/cleanup-coordinator-enhanced/modules/CleanupUtilities.ts", "headerStatus": "v2.1", "lines": 206, "size": 6280, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/cleanup-coordinator-enhanced/modules/RollbackUtilities.ts", "headerStatus": "v2.1", "lines": 339, "size": 12400, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/cleanup-coordinator-enhanced/modules/SystemOrchestrator.ts", "headerStatus": "v2.1", "lines": 733, "size": 25398, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/cleanup-coordinator-enhanced/modules/DependencyResolver.ts", "headerStatus": "v2.1", "lines": 746, "size": 24597, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/cleanup-coordinator-enhanced/modules/UtilityPerformance.ts", "headerStatus": "v2.1", "lines": 633, "size": 22601, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/cleanup-coordinator-enhanced/modules/UtilityExecution.ts", "headerStatus": "v2.1", "lines": 364, "size": 12046, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/cleanup-coordinator-enhanced/modules/TemplateDependencies.ts", "headerStatus": "v2.1", "lines": 642, "size": 18531, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/cleanup-coordinator-enhanced/modules/TemplateValidation.ts", "headerStatus": "v2.1", "lines": 967, "size": 33691, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/cleanup-coordinator-enhanced/modules/HealthStatusManager.ts", "headerStatus": "v2.1", "lines": 387, "size": 13213, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/cleanup-coordinator-enhanced/modules/TimingInfrastructureManager.ts", "headerStatus": "v2.1", "lines": 360, "size": 11888, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/cleanup-coordinator-enhanced/modules/UtilityValidation.ts", "headerStatus": "v2.1", "lines": 435, "size": 14521, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/cleanup-coordinator-enhanced/modules/UtilityAnalysis.ts", "headerStatus": "v2.1", "lines": 250, "size": 8667, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/cleanup-coordinator-enhanced/modules/AsyncErrorHandler.ts", "headerStatus": "v2.1", "lines": 355, "size": 11204, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/cleanup-coordinator-enhanced/modules/TemplateWorkflows.ts", "headerStatus": "v2.1", "lines": 1058, "size": 36183, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/cleanup-coordinator-enhanced/modules/RollbackManager.ts", "headerStatus": "v2.1", "lines": 848, "size": 29139, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/cleanup-coordinator-enhanced/modules/InitializationManager.ts", "headerStatus": "v2.1", "lines": 326, "size": 11735, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/cleanup-coordinator-enhanced/modules/OperationExecutionManager.ts", "headerStatus": "v2.1", "lines": 794, "size": 28520, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/interfaces/tracking/core-interfaces.ts", "headerStatus": "v2.1", "lines": 464, "size": 11348, "tier": "shared", "category": "typescript", "priority": "medium"}, {"path": "./shared/src/interfaces/tracking/notification-interfaces.ts", "headerStatus": "v2.1", "lines": 115, "size": 3950, "tier": "shared", "category": "typescript", "priority": "medium"}, {"path": "./shared/src/interfaces/tracking/tracking-interfaces.ts", "headerStatus": "v2.1", "lines": 280, "size": 6779, "tier": "shared", "category": "typescript", "priority": "medium"}, {"path": "./shared/src/interfaces/governance/management-configuration/governance-rule-environment-manager.ts", "headerStatus": "v2.1", "lines": 350, "size": 8585, "tier": "shared", "category": "typescript", "priority": "medium"}, {"path": "./shared/src/interfaces/governance/management-configuration/governance-rule-documentation-generator.ts", "headerStatus": "v2.1", "lines": 3737, "size": 92172, "tier": "shared", "category": "typescript", "priority": "medium"}, {"path": "./shared/src/interfaces/governance/management-configuration/tracking-system-guide-generator.ts", "headerStatus": "v2.1", "lines": 1289, "size": 30938, "tier": "shared", "category": "typescript", "priority": "medium"}, {"path": "./shared/src/constants/platform/tracking/environment-constants-calculator.ts", "headerStatus": "v2.1", "lines": 735, "size": 26736, "tier": "shared", "category": "typescript", "priority": "medium"}, {"path": "./shared/src/constants/platform/tracking/tracking-constants.ts", "headerStatus": "v2.1", "lines": 359, "size": 11430, "tier": "shared", "category": "typescript", "priority": "medium"}, {"path": "./shared/src/constants/platform/tracking/tracking-constants-enhanced.ts", "headerStatus": "v2.1", "lines": 734, "size": 22290, "tier": "shared", "category": "typescript", "priority": "medium"}, {"path": "./shared/src/constants/tracking/tracking-management-constants.ts", "headerStatus": "v2.1", "lines": 395, "size": 10206, "tier": "shared", "category": "typescript", "priority": "medium"}, {"path": "./docs/es6-oa-standard-imp-plan.md", "headerStatus": "no-header", "lines": 999, "size": 41898, "tier": "unknown", "category": "documentation", "priority": "low"}, {"path": "./docs/phase-2-completion-report.md", "headerStatus": "no-header", "lines": 138, "size": 6636, "tier": "unknown", "category": "documentation", "priority": "low"}, {"path": "./docs/dir-org-prompt.md", "headerStatus": "no-header", "lines": 976, "size": 36258, "tier": "unknown", "category": "documentation", "priority": "low"}, {"path": "./docs/es6-imp-plan.md", "headerStatus": "no-header", "lines": 561, "size": 16578, "tier": "unknown", "category": "documentation", "priority": "low"}, {"path": "./docs/plan/milestone-m11a-business-application-registry.md", "headerStatus": "no-header", "lines": 1908, "size": 150651, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/plan/milestone-06-plugin-system.md", "headerStatus": "no-header", "lines": 1003, "size": 58762, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/plan/milestone-08-advanced-governance-consolidated.md", "headerStatus": "v2.0-or-older", "lines": 1509, "size": 105122, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/plan/Milestone-11-external-database-management.md", "headerStatus": "v2.0-or-older", "lines": 2573, "size": 184041, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/plan/milestones-index.md", "headerStatus": "no-header", "lines": 265, "size": 14226, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/plan/implemented/m0-audit-report.md", "headerStatus": "no-header", "lines": 452, "size": 39433, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/plan/implemented/m0-classes-diagram.md", "headerStatus": "no-header", "lines": 618, "size": 24612, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/plan/implemented/milestone-00-enhancements-m0.1.md", "headerStatus": "no-header", "lines": 997, "size": 54359, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/plan/milestone-07-production-ready.md", "headerStatus": "no-header", "lines": 2129, "size": 115387, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/plan/readme.md", "headerStatus": "no-header", "lines": 110, "size": 8970, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/plan/milestone-02-governance-integrated.md", "headerStatus": "no-header", "lines": 1112, "size": 71808, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/plan/milestone-00.2-unified-api-gateway.md", "headerStatus": "no-header", "lines": 573, "size": 23995, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/plan/milestone-04a-administration-interface.md", "headerStatus": "no-header", "lines": 1639, "size": 136577, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/plan/milestone-01b-bootstrap.md", "headerStatus": "v2.0-or-older", "lines": 1464, "size": 108020, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/plan/milestone-m11a_i-integration.md", "headerStatus": "no-header", "lines": 494, "size": 32689, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/plan/milestone-05-realtime-features.md", "headerStatus": "no-header", "lines": 591, "size": 25539, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/plan/milestone-01c-business-application-foundation.md", "headerStatus": "no-header", "lines": 1410, "size": 89969, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/plan/milestones-ndx-enh.md", "headerStatus": "no-header", "lines": 945, "size": 86000, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/plan/milestone-01a-foundation-for-m11.md", "headerStatus": "v2.0-or-older", "lines": 1164, "size": 81010, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/plan/milestone-03-user-dashboard.md", "headerStatus": "v2.0-or-older", "lines": 1098, "size": 67870, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/plan/milestone-07a-foundation-for-m11.md", "headerStatus": "no-header", "lines": 1505, "size": 92088, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/plan/milestone-00a-business-app-gov-ext.md", "headerStatus": "no-header", "lines": 455, "size": 29222, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/plan/milestone-m6.1-universal-standards-cartridge-system.md", "headerStatus": "no-header", "lines": 2854, "size": 107294, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/plan/milestone-07b-framework-enterprise-infra.md", "headerStatus": "v2.0-or-older", "lines": 1453, "size": 115383, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/plan/milestone-m11b-resource-inheritance-framework.md", "headerStatus": "no-header", "lines": 2007, "size": 164540, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/plan/milestone-02a-application-authentication.md", "headerStatus": "v2.0-or-older", "lines": 1819, "size": 140001, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/plan/milestone-04-admin-panel.md", "headerStatus": "no-header", "lines": 937, "size": 60953, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/plan/milestone-00-governance-tracking.md", "headerStatus": "no-header", "lines": 2216, "size": 148880, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/plan/milestone-00-enhancements-m0.1.md", "headerStatus": "no-header", "lines": 1225, "size": 71408, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/plan/milestone-01-governance-first.md", "headerStatus": "v2.0-or-older", "lines": 958, "size": 56993, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/todo.md", "headerStatus": "no-header", "lines": 158, "size": 10178, "tier": "unknown", "category": "documentation", "priority": "low"}, {"path": "./docs/processes/development-workflow.md", "headerStatus": "no-header", "lines": 1276, "size": 58402, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/processes/ai-command-reference.md", "headerStatus": "no-header", "lines": 742, "size": 25456, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/policies/template-creation-policy-override.md", "headerStatus": "no-header", "lines": 336, "size": 10870, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/tracking/coverage-milestone-progress-2025-01-15.md", "headerStatus": "no-header", "lines": 159, "size": 6438, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/tracking/tracking-system.md", "headerStatus": "no-header", "lines": 1510, "size": 67296, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/tracking/tracking-system-activation.md", "headerStatus": "no-header", "lines": 266, "size": 9286, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/tracking/README.md", "headerStatus": "no-header", "lines": 116, "size": 4784, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/tracking/logging-system.md", "headerStatus": "no-header", "lines": 1146, "size": 36565, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/tracking/refactoring-governance-summary-2025-06-23.md", "headerStatus": "no-header", "lines": 223, "size": 9968, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/demos-prompts/m0-demo/hand-off-05.md", "headerStatus": "no-header", "lines": 398, "size": 16227, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/demos-prompts/m0-demo/m0-demo-prompt.md", "headerStatus": "no-header", "lines": 784, "size": 36823, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/demos-prompts/m0-demo/m0-demo-implementation-plan.md", "headerStatus": "no-header", "lines": 1056, "size": 49201, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/demos-prompts/m0-demo/hand-off-06.md", "headerStatus": "no-header", "lines": 263, "size": 9960, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/demos-prompts/m0-demo/hand-off-02.md", "headerStatus": "no-header", "lines": 316, "size": 13542, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/demos-prompts/m0-demo/hand-off-03.md", "headerStatus": "no-header", "lines": 318, "size": 10454, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/demos-prompts/m0-demo/hand-off-04.md", "headerStatus": "no-header", "lines": 326, "size": 13007, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/demos-prompts/m0-demo/hand-off.md", "headerStatus": "no-header", "lines": 469, "size": 18194, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/demos-prompts/m0-demo/m0-demo-plan-report.md", "headerStatus": "no-header", "lines": 85, "size": 4535, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/demos-prompts/m0-real-demo/m0-prompt-for-plan.md", "headerStatus": "no-header", "lines": 1541, "size": 66076, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/demos-prompts/m0-real-demo/old-docs/tracking-update-summary.md", "headerStatus": "no-header", "lines": 167, "size": 6249, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/demos-prompts/m0-real-demo/old-docs/project-completion-summary.md", "headerStatus": "no-header", "lines": 211, "size": 9821, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/demos-prompts/m0-real-demo/old-docs/documentation-sync-summary.md", "headerStatus": "no-header", "lines": 147, "size": 6931, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/demos-prompts/m0-real-demo/old-docs/documentation-final-update.md", "headerStatus": "no-header", "lines": 225, "size": 10105, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/demos-prompts/m0-real-demo/old-docs/m0-dash-plan.md", "headerStatus": "no-header", "lines": 301, "size": 15303, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/core/template-system.md", "headerStatus": "minimal-header", "lines": 1119, "size": 48071, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/core/development-standards-v21.md", "headerStatus": "no-header", "lines": 994, "size": 47486, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/core/governance-process.md", "headerStatus": "no-header", "lines": 911, "size": 30029, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/core/automatic-universal-governance-driver-v7.1.md", "headerStatus": "v2.0-or-older", "lines": 1068, "size": 48600, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/core/orchestration-driver.md", "headerStatus": "no-header", "lines": 1947, "size": 72849, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/core/session-management.md", "headerStatus": "no-header", "lines": 953, "size": 35326, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/ai/ai-instructions.md", "headerStatus": "no-header", "lines": 900, "size": 53116, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/ai/OA-FRAMEWORK-DISCOVERY-INSTRUCTIONS.md", "headerStatus": "no-header", "lines": 257, "size": 7285, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/coverage-decisions/TemplateDependencies-Coverage-Decision.md", "headerStatus": "no-header", "lines": 133, "size": 6283, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/contexts/framework/guides/AI-PROMPT-EXECUTION-SUMMARY.md", "headerStatus": "no-header", "lines": 255, "size": 8522, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/contexts/framework/guides/OA-FRAMEWORK-QUICK-START.md", "headerStatus": "no-header", "lines": 52, "size": 1681, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/contexts/framework/guides/IMPLEMENTATION_GUIDE_FOR_NOVICE.md", "headerStatus": "no-header", "lines": 542, "size": 19396, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/contexts/framework/README.md", "headerStatus": "no-header", "lines": 58, "size": 2706, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/contexts/user-experience-context/README.md", "headerStatus": "no-header", "lines": 246, "size": 11309, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/contexts/governance/guides/G-TSK-02-ADVANCED-RULE-MANAGEMENT-COMPLETION-REPORT.md", "headerStatus": "no-header", "lines": 206, "size": 7939, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/contexts/governance/guides/DOCUMENTATION_COMPLIANCE_ASSESSMENT.md", "headerStatus": "no-header", "lines": 270, "size": 10390, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/contexts/governance/guides/REMEDIATION_COMPLETION_SUMMARY.md", "headerStatus": "no-header", "lines": 234, "size": 10071, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/contexts/governance/system/AI-G-TSK-02-IMPLEMENTATION-PROMPT.md", "headerStatus": "no-header", "lines": 298, "size": 11974, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/contexts/README.md", "headerStatus": "no-header", "lines": 175, "size": 7488, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/contexts/authentication-context/README.md", "headerStatus": "no-header", "lines": 228, "size": 10897, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/contexts/foundation-context/guides/performance-optimization.md", "headerStatus": "v2.1", "lines": 857, "size": 25384, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/contexts/foundation-context/guides/imp-plan.md", "headerStatus": "no-header", "lines": 400, "size": 16357, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/contexts/foundation-context/guides/memory-safe-resource-manager-enhanced-integration.md", "headerStatus": "v2.1", "lines": 641, "size": 17594, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/contexts/foundation-context/guides/migration-enhanced.md", "headerStatus": "v2.1", "lines": 687, "size": 18190, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/contexts/foundation-context/guides/M0-COMPONENT-TESTING-PLAN.md", "headerStatus": "no-header", "lines": 1083, "size": 40389, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/contexts/foundation-context/guides/imp-plan-confirmation-completion.md", "headerStatus": "no-header", "lines": 300, "size": 9298, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/contexts/foundation-context/api/memory-safe-resource-manager-enhanced-api.md", "headerStatus": "v2.1", "lines": 683, "size": 16009, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/contexts/foundation-context/components/memory-safe-resource-manager-enhanced.md", "headerStatus": "v2.1", "lines": 541, "size": 15281, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/contexts/foundation-context/tracking/logs/tracking-log-2025-06-28.md", "headerStatus": "no-header", "lines": 1, "size": 1, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/contexts/foundation-context/services/governance-admin-training-system.md", "headerStatus": "no-header", "lines": 394, "size": 14248, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/contexts/foundation-context/services/governance-tracking-bridge.md", "headerStatus": "no-header", "lines": 815, "size": 28240, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/contexts/foundation-context/services/base-tracking-service.md", "headerStatus": "no-header", "lines": 191, "size": 3980, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/contexts/foundation-context/services/realtime-event-coordinator.md", "headerStatus": "no-header", "lines": 990, "size": 34744, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/contexts/foundation-context/services/authority-compliance-monitor-bridge.md", "headerStatus": "no-header", "lines": 1121, "size": 41633, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/contexts/foundation-context/services/tracking-system-guide-generator.md", "headerStatus": "no-header", "lines": 480, "size": 16512, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/contexts/foundation-context/services/memory-safety-integration-validator.md", "headerStatus": "no-header", "lines": 966, "size": 38035, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/contexts/foundation-context/services/governance-system-doc-generator.md", "headerStatus": "no-header", "lines": 271, "size": 9584, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/contexts/foundation-context/services/cross-reference-validation-bridge.md", "headerStatus": "no-header", "lines": 1095, "size": 39837, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/contexts/foundation-context/governance/security/README.md", "headerStatus": "no-header", "lines": 224, "size": 4576, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/contexts/foundation-context/governance/performance/rule-resource-manager.md", "headerStatus": "no-header", "lines": 292, "size": 7785, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/contexts/foundation-context/governance/performance/rule-cache-manager-es2020-compliance.md", "headerStatus": "no-header", "lines": 129, "size": 4511, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/contexts/foundation-context/governance/performance/README.md", "headerStatus": "no-header", "lines": 166, "size": 3648, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/contexts/foundation-context/governance/performance/rule-performance-profiler.md", "headerStatus": "no-header", "lines": 1, "size": 1, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/contexts/foundation-context/governance/performance/rule-performance-optimizer.md", "headerStatus": "no-header", "lines": 277, "size": 7333, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/contexts/foundation-context/governance/performance/rule-cache-manager.md", "headerStatus": "no-header", "lines": 297, "size": 7468, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/contexts/foundation-context/governance/monitoring/README.md", "headerStatus": "no-header", "lines": 259, "size": 4949, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/contexts/foundation-context/README.md", "headerStatus": "no-header", "lines": 373, "size": 15501, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/contexts/foundation-context/constants/smart-environment-constants.md", "headerStatus": "no-header", "lines": 389, "size": 14297, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/contexts/foundation-context/constants/environment-calculator.md", "headerStatus": "no-header", "lines": 199, "size": 4594, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/contexts/enterprise-context/README.md", "headerStatus": "no-header", "lines": 249, "size": 12943, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/contexts/production-context/README.md", "headerStatus": "no-header", "lines": 246, "size": 12324, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/api-list-report.md", "headerStatus": "no-header", "lines": 618, "size": 35621, "tier": "unknown", "category": "documentation", "priority": "low"}, {"path": "./docs/governance/tracking/reports/phase-b-completion-report-2025-07-25.md", "headerStatus": "no-header", "lines": 268, "size": 13265, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/governance/tracking/reports/G-TSK-06-Corrected-Tracking-Sync-Report-20250703.md", "headerStatus": "no-header", "lines": 177, "size": 7680, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/governance/tracking/reports/COMPREHENSIVE-TRACKING-SYNC-AUDIT-20250703.md", "headerStatus": "no-header", "lines": 296, "size": 11615, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/governance/tracking/reports/G-TSK-04-SUB-04-2-COMPLIANCE-INFRASTRUCTURE-TRACKING-REPORT.md", "headerStatus": "no-header", "lines": 189, "size": 8326, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/governance/tracking/reports/M0-SECURITY-INTEGRATION-DOCUMENTATION.md", "headerStatus": "no-header", "lines": 201, "size": 6493, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/governance/tracking/reports/G-TSK-06-Completion-Status-Update-20250703.md", "headerStatus": "no-header", "lines": 181, "size": 7695, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/governance/tracking/reports/G-SUB-05-2-PROCESSING-FRAMEWORK-AUDIT-REPORT.md", "headerStatus": "no-header", "lines": 338, "size": 16340, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/governance/tracking/reports/DEVELOPMENT-RESUMPTION-NOTIFICATION.md", "headerStatus": "no-header", "lines": 90, "size": 2932, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/governance/tracking/reports/Implementation-Task-ID-Compliance-Update-20250703.md", "headerStatus": "no-header", "lines": 180, "size": 9247, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/governance/tracking/reports/governance-implementation-report-20250629.md", "headerStatus": "no-header", "lines": 104, "size": 6901, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/governance/tracking/reports/g-sub-05.2-processing-framework-implementation-report-20250630.md", "headerStatus": "no-header", "lines": 341, "size": 15013, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/governance/tracking/reports/CRITICAL-SMART-CONSTANTS-INTEGRATION-STATUS.md", "headerStatus": "no-header", "lines": 170, "size": 8179, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/governance/tracking/reports/EMERGENCY-DEVELOPMENT-HALT-NOTIFICATION.md", "headerStatus": "no-header", "lines": 255, "size": 11406, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/governance/tracking/reports/M0-EMERGENCY-SECURITY-INTEGRATION-PLAN.md", "headerStatus": "no-header", "lines": 235, "size": 10139, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/governance/tracking/reports/TRACKING-SYSTEM-REORGANIZATION-NOTIFICATION.md", "headerStatus": "no-header", "lines": 116, "size": 3316, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/governance/tracking/reports/CORRECTED-TRACKING-SYNC-AUDIT-20250703.md", "headerStatus": "no-header", "lines": 178, "size": 7234, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/governance/tracking/reports/G-TSK-06-Phase-8-Completion-Report-20250703.md", "headerStatus": "no-header", "lines": 285, "size": 10287, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/governance/tracking/reports/g-tsk-06-documentation-compliance-session-report.md", "headerStatus": "no-header", "lines": 243, "size": 10676, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/governance/tracking/reports/G-TSK-06-Architecture-Fix-Completion-Report-20250703.md", "headerStatus": "no-header", "lines": 255, "size": 10732, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/governance/tracking/reports/development-activation-report.md", "headerStatus": "no-header", "lines": 145, "size": 5314, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/governance/tracking/status/README.md", "headerStatus": "no-header", "lines": 111, "size": 3724, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/governance/tracking/rules/AI-ASSISTANT-DOCUMENT-ORGANIZATION-RULES.md", "headerStatus": "no-header", "lines": 124, "size": 3631, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/governance/tracking/documentation/CROSS-REFERENCE-UPDATE-TASK.md", "headerStatus": "no-header", "lines": 103, "size": 3297, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/governance/tracking/documentation/AUTHORITY-APPROVAL-enhanced-services-refactoring-2025-07-24.md", "headerStatus": "no-header", "lines": 210, "size": 10743, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/governance/tracking/documentation/DOCUMENTATION-REOR<PERSON><PERSON>ZATION-REPORT.md", "headerStatus": "no-header", "lines": 85, "size": 3180, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/governance/tracking/documentation/governance-tracking-dashboard.md", "headerStatus": "no-header", "lines": 245, "size": 8356, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/governance/tracking/documentation/DCR-foundation-003-enhanced-services-modularization.md", "headerStatus": "no-header", "lines": 295, "size": 13374, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/governance/tracking/documentation/ADR-foundation-003-enhanced-services-refactoring.md", "headerStatus": "no-header", "lines": 202, "size": 10055, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/governance/scripts/update-rule-integrity.js", "headerStatus": "no-header", "lines": 37, "size": 1285, "tier": "docs", "category": "javascript", "priority": "low"}, {"path": "./docs/governance/scripts/create-rule-files.js", "headerStatus": "no-header", "lines": 52, "size": 1483, "tier": "docs", "category": "javascript", "priority": "low"}, {"path": "./docs/governance/scripts/validate-all-rules.js", "headerStatus": "no-header", "lines": 56, "size": 1791, "tier": "docs", "category": "javascript", "priority": "low"}, {"path": "./docs/governance/IMPLEMENTATION_SUMMARY.md", "headerStatus": "no-header", "lines": 126, "size": 5902, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/governance/contexts/foundation-context/05-implementation/G-TSK-02-ADVANCED-RULE-MANAGEMENT-COMPLETION-REPORT.md", "headerStatus": "no-header", "lines": 236, "size": 9056, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/governance/contexts/foundation-context/05-implementation/G-TSK-03-PERFORMANCE-MONITORING-IMPLEMENTATION-REPORT.md", "headerStatus": "no-header", "lines": 214, "size": 5790, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/governance/contexts/foundation-context/04-review/REV-foundation-20250621-authority-approval.md", "headerStatus": "no-header", "lines": 194, "size": 8678, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/governance/contexts/foundation-context/04-review/REV-foundation-20250817-m0.2-authority-approval.md", "headerStatus": "no-header", "lines": 300, "size": 14679, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/governance/contexts/foundation-context/01-discussion/DISC-foundation-20250817-m0.2-implementation-discussion.md", "headerStatus": "no-header", "lines": 262, "size": 12700, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/governance/contexts/foundation-context/01-discussion/DISC-foundation-20250621-tracking-architecture-options.md", "headerStatus": "no-header", "lines": 386, "size": 17091, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/governance/contexts/foundation-context/03-dcr/DCR-foundation-009-m0-scope-expansion-memory-safety.md", "headerStatus": "no-header", "lines": 482, "size": 23594, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/governance/contexts/foundation-context/03-dcr/DCR-foundation-003-m0.2-gateway-development-standards.md", "headerStatus": "no-header", "lines": 519, "size": 17404, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/governance/contexts/foundation-context/03-dcr/DCR-foundation-010-timer-coordination-development.md", "headerStatus": "no-header", "lines": 211, "size": 9409, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/governance/contexts/foundation-context/03-dcr/DCR-foundation-007-configuration-management-standards.md", "headerStatus": "no-header", "lines": 644, "size": 20500, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/governance/contexts/foundation-context/03-dcr/DCR-foundation-001-tracking-development.md", "headerStatus": "no-header", "lines": 170, "size": 7150, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/governance/contexts/foundation-context/03-dcr/DCR-foundation-004-g-tsk-06-security-implementation.md", "headerStatus": "no-header", "lines": 498, "size": 16389, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/governance/contexts/foundation-context/03-dcr/DCR-foundation-006-g-tsk-07-security-integration-standards.md", "headerStatus": "no-header", "lines": 401, "size": 15402, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/governance/contexts/foundation-context/03-dcr/DCR-foundation-003-smart-tracking.md", "headerStatus": "no-header", "lines": 107, "size": 2625, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/governance/contexts/foundation-context/03-dcr/DCR-foundation-002-smart-constants.md", "headerStatus": "no-header", "lines": 90, "size": 2369, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/governance/contexts/foundation-context/03-dcr/DCR-foundation-005-performance-standards.md", "headerStatus": "no-header", "lines": 88, "size": 2603, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/governance/contexts/foundation-context/03-dcr/DCR-foundation-008-template-security-standards.md", "headerStatus": "no-header", "lines": 540, "size": 21578, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/governance/contexts/foundation-context/03-dcr/DCR-foundation-002-enhanced-implementation-standards.md", "headerStatus": "no-header", "lines": 466, "size": 16788, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/governance/contexts/foundation-context/03-dcr/DCR-foundation-005-g-tsk-06-performance-standards.md", "headerStatus": "no-header", "lines": 730, "size": 22103, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/governance/contexts/foundation-context/04-summary/adr_implementation_alignment.md", "headerStatus": "no-header", "lines": 373, "size": 14654, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/governance/contexts/foundation-context/04-summary/G-TSK-07-Documentation-Summary.md", "headerStatus": "no-header", "lines": 512, "size": 21681, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/governance/contexts/foundation-context/04-summary/G-TSK-06-Documentation-Summary.md", "headerStatus": "no-header", "lines": 305, "size": 16397, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/governance/contexts/foundation-context/02-adr/ADR-foundation-004-analytics-reporting-architecture.md", "headerStatus": "no-header", "lines": 368, "size": 14375, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/governance/contexts/foundation-context/02-adr/ADR-foundation-008-configuration-deployment-architecture.md", "headerStatus": "no-header", "lines": 340, "size": 14136, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/governance/contexts/foundation-context/02-adr/ADR-foundation-009-template-security-architecture.md", "headerStatus": "no-header", "lines": 541, "size": 20205, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/governance/contexts/foundation-context/02-adr/ADR-foundation-002-environment-adaptation.md", "headerStatus": "no-header", "lines": 77, "size": 2290, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/governance/contexts/foundation-context/02-adr/ADR-foundation-003-m0.2-gateway-implementation.md", "headerStatus": "no-header", "lines": 370, "size": 13877, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/governance/contexts/foundation-context/02-adr/ADR-foundation-011-timer-coordination-refactoring.md", "headerStatus": "no-header", "lines": 165, "size": 7696, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/governance/contexts/foundation-context/02-adr/ADR-foundation-006-realtime-analytics-optimization.md", "headerStatus": "no-header", "lines": 425, "size": 16819, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/governance/contexts/foundation-context/02-adr/ADR-foundation-010-memory-safety-architecture.md", "headerStatus": "no-header", "lines": 459, "size": 19489, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/governance/contexts/foundation-context/02-adr/ADR-foundation-003-adaptive-constants.md", "headerStatus": "no-header", "lines": 80, "size": 2326, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/governance/contexts/foundation-context/02-adr/ADR-foundation-001-tracking-architecture.md", "headerStatus": "no-header", "lines": 470, "size": 21256, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/governance/contexts/foundation-context/02-adr/ADR-foundation-007-management-administration-architecture.md", "headerStatus": "no-header", "lines": 400, "size": 17795, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/governance/contexts/foundation-context/02-adr/ADR-foundation-005-compliance-regulatory-framework.md", "headerStatus": "no-header", "lines": 497, "size": 18119, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/governance/README.md", "headerStatus": "no-header", "lines": 52, "size": 1545, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/governance/templates/component-header-standard.md", "headerStatus": "no-header", "lines": 164, "size": 5197, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/README.md", "headerStatus": "no-header", "lines": 419, "size": 21830, "tier": "unknown", "category": "documentation", "priority": "low"}, {"path": "./docs/issues-with-x-models.md", "headerStatus": "no-header", "lines": 1439, "size": 73267, "tier": "unknown", "category": "documentation", "priority": "low"}, {"path": "./docs/phase-4-completion-report.md", "headerStatus": "no-header", "lines": 206, "size": 10963, "tier": "unknown", "category": "documentation", "priority": "low"}, {"path": "./docs/templates/governance-workflow-templates.md", "headerStatus": "no-header", "lines": 487, "size": 14922, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/templates/milestone-extension-template.md", "headerStatus": "v2.0-or-older", "lines": 271, "size": 10173, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/templates/template-compliance-framework.md", "headerStatus": "minimal-header", "lines": 470, "size": 18478, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/templates/core-component-templates.md", "headerStatus": "minimal-header", "lines": 570, "size": 18598, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/templates/security-validation-templates.md", "headerStatus": "minimal-header", "lines": 761, "size": 25215, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/templates/ai-collaboration-templates.md", "headerStatus": "minimal-header", "lines": 841, "size": 27655, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/implementation/G-SUB-04.2-compliance-infrastructure-implementation-summary.md", "headerStatus": "no-header", "lines": 340, "size": 15791, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/lessons/jest-coverage-limitations-workarounds.md", "headerStatus": "no-header", "lines": 424, "size": 13798, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/lessons/lesson-13-perfect-coverage-mastery.md", "headerStatus": "no-header", "lines": 323, "size": 11313, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/lessons/lesson-17-cleanupconfig-anti-simplification-achievement.md", "headerStatus": "no-header", "lines": 336, "size": 13451, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/lessons/lesson-01-GovernanceTrackingSystem-Integration.md", "headerStatus": "no-header", "lines": 536, "size": 21371, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/lessons/lesson-learned-09-CleanupCoordinatorEnhanced.md", "headerStatus": "no-header", "lines": 479, "size": 17782, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/lessons/perfect-coverage-methodology.md", "headerStatus": "no-header", "lines": 587, "size": 18972, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/lessons/resilient-timing-surgical-precision-success.md", "headerStatus": "no-header", "lines": 240, "size": 8509, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/lessons/analysis/RollbackUtilities-Analysis.md", "headerStatus": "no-header", "lines": 156, "size": 7717, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/lessons/analysis/RollbackSnapshots-Analysis.md", "headerStatus": "no-header", "lines": 123, "size": 5267, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/lessons/lesson-learned-07-EventHandlerRegistry.md", "headerStatus": "no-header", "lines": 860, "size": 31598, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/lessons/lesson-learned-04-TimerCoordinationService.md", "headerStatus": "no-header", "lines": 439, "size": 13857, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/lessons/enhanced-services-integration-master-summary.md", "headerStatus": "no-header", "lines": 239, "size": 11531, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/lessons/lesson-16-dependency-resolver-perfect-coverage-mastery.md", "headerStatus": "no-header", "lines": 297, "size": 11579, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/lessons/workflows/coverage-workflow.md", "headerStatus": "no-header", "lines": 159, "size": 5144, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/lessons/lesson-19-component-discovery-manager-perfect-coverage.md", "headerStatus": "no-header", "lines": 248, "size": 9119, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/lessons/governance-compliance-checker-comprehensive-achievement-summary.md", "headerStatus": "no-header", "lines": 277, "size": 11013, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/lessons/quick-reference/100-percent-coverage-guide.md", "headerStatus": "no-header", "lines": 95, "size": 2448, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/lessons/lesson-20-enhanced-services-integration-jest-mastery.md", "headerStatus": "no-header", "lines": 462, "size": 18950, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/lessons/feedback/pattern-effectiveness.md", "headerStatus": "no-header", "lines": 556, "size": 24876, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/lessons/lesson-learned-24-MemorySafeResourceManagerEnhanced-branch-coverage.md", "headerStatus": "no-header", "lines": 191, "size": 10924, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/lessons/lesson-25-governance-security-race-condition-resolution.md", "headerStatus": "no-header", "lines": 311, "size": 11125, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/lessons/lesson-15-branch-coverage-resolution-mastery.md", "headerStatus": "no-header", "lines": 531, "size": 21358, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/lessons/lesson-learned-06-MemorySafetyManager.md", "headerStatus": "no-header", "lines": 802, "size": 28088, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/lessons/lesson-learned-11-RollbackManager.md", "headerStatus": "no-header", "lines": 292, "size": 11293, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/lessons/lesson-learned-14-CleanupCoordinatorEnhanced-comprehensive-coverage.md", "headerStatus": "no-header", "lines": 550, "size": 19711, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/lessons/README.md", "headerStatus": "no-header", "lines": 600, "size": 38723, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/lessons/templates/catch-block-coverage.template.ts", "headerStatus": "no-header", "lines": 208, "size": 8127, "tier": "docs", "category": "typescript", "priority": "low"}, {"path": "./docs/lessons/lesson-learned-10-CleanupCoordinatorEnhanced.md", "headerStatus": "no-header", "lines": 1123, "size": 39698, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/lessons/lesson-28-governance-tracking-bridge-branch-coverage-mastery.md", "headerStatus": "no-header", "lines": 294, "size": 12525, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/lessons/timing-coverage-jest-limitations.md", "headerStatus": "no-header", "lines": 39, "size": 3006, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/lessons/rollback-manager-surgical-precision-mastery.md", "headerStatus": "no-header", "lines": 594, "size": 22772, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/lessons/lesson-learned-03-AnalyticsTrackingEngine.md", "headerStatus": "no-header", "lines": 345, "size": 11894, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/lessons/lesson-23-enhanced-services-integration-performance-optimization.md", "headerStatus": "minimal-header", "lines": 563, "size": 23315, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/lessons/lesson-21-jest-fake-timer-compatibility-patterns.md", "headerStatus": "no-header", "lines": 442, "size": 16346, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/lessons/lesson-learned-08-TimerCoordinationService.md", "headerStatus": "no-header", "lines": 825, "size": 31706, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/lessons/lesson-12-TimerCoordinationPatterns.md", "headerStatus": "no-header", "lines": 445, "size": 14084, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/lessons/quick-reference.md", "headerStatus": "minimal-header", "lines": 736, "size": 25152, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/lessons/lesson-learned-26-trackingmanager-enterprise-coverage-mastery.md", "headerStatus": "no-header", "lines": 321, "size": 12556, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/lessons/lesson-29-surgical-precision-branch-coverage-phase-2-mastery.md", "headerStatus": "no-header", "lines": 318, "size": 15602, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/lessons/lesson-02-GovernanceTrackingSystem-timeout.md", "headerStatus": "no-header", "lines": 435, "size": 17189, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/lessons/lesson-18-cleanup-template-manager-coverage.md", "headerStatus": "no-header", "lines": 241, "size": 10173, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/lessons/lesson-learned-05-MemorySafeResourceManager.md", "headerStatus": "minimal-header", "lines": 294, "size": 9809, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/lessons/lesson-27-typescript-eslint-error-resolution-coverage-preservation.md", "headerStatus": "minimal-header", "lines": 327, "size": 10300, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/refactors/refactoring-plan-2025-06-23.md", "headerStatus": "no-header", "lines": 848, "size": 33791, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/refactors/refactoring-completion-report-2025-06-23.md", "headerStatus": "no-header", "lines": 262, "size": 11626, "tier": "docs", "category": "documentation", "priority": "low"}, {"path": "./docs/phase-3-completion-report.md", "headerStatus": "no-header", "lines": 173, "size": 8396, "tier": "unknown", "category": "documentation", "priority": "low"}, {"path": "./docs/hand-off-docs/performance-resilence.md", "headerStatus": "no-header", "lines": 225, "size": 8228, "tier": "docs", "category": "documentation", "priority": "low"}]}