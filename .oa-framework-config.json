{"oaFrameworkConfig": {"version": "2.4.0", "configType": "OA_FRAMEWORK_DISCOVERY_CONFIGURATION", "lastUpdated": "2025-09-12T00:00:00Z", "authority": "President & CEO, E.Z. Consultancy", "purpose": "Single source of truth for OA Framework file locations and system discovery", "templatePolicy": "on-demand-creation-with-policy-override", "projectStructure": "server-shared-client-architecture", "enhancedOrchestrationIntegration": "6.4.0", "trackingConsolidation": "COMPLETE", "m0_1_integration": "ACTIVE"}, "frameworkPaths": {"rootDirectory": ".", "governanceRoot": "docs/governance", "trackingDirectory": "docs/tracking", "milestonesDirectory": "docs/plan", "templatesDirectory": "docs/templates", "indexesDirectory": "docs/governance/indexes", "crossCuttingDirectory": "docs/governance/cross-cutting", "archiveDirectory": "docs/governance/archive", "rulesDirectory": "docs/governance/rules", "scriptsDirectory": "docs/governance/scripts", "coreDirectory": "docs/core", "processesDirectory": "docs/processes", "policiesDirectory": "docs/policies", "aiDirectory": "docs/ai"}, "trackingFiles": {"implementationProgress": "docs/governance/tracking/status/.oa-implementation-progress.json", "sessionLog": "docs/governance/tracking/.oa-session-log.jsonl", "governanceCompliance": "docs/governance/tracking/status/.oa-governance-compliance.json", "analyticsCache": "docs/governance/tracking/status/.oa-analytics-cache.json", "smartPathResolution": "docs/governance/tracking/status/.oa-smart-path-resolution.json", "crossReferenceValidation": "docs/governance/tracking/status/.oa-cross-reference-validation.json", "authorityCompliance": "docs/governance/tracking/status/.oa-authority-compliance.json", "orchestrationCoordination": "docs/governance/tracking/status/.oa-orchestration-coordination.json", "trackingReadme": "docs/tracking/README.md", "governanceGateStatus": "docs/governance/tracking/.oa-governance-gate-status.json"}, "coreFrameworkFiles": {"enhancedOrchestrationDriver": "docs/core/orchestration-driver.md", "automaticUniversalGovernanceDriver": "docs/core/automatic-universal-governance-driver-v7.1.md", "unifiedTrackingSystem": "docs/tracking/tracking-system.md", "trackingSystemActivation": "docs/tracking/tracking-system-activation.md", "sessionManagement": "docs/core/session-management.md", "templateSystem": "docs/core/template-system.md", "governanceProcess": "docs/core/governance-process.md", "developmentStandards": "docs/core/development-standards-v21.md", "unifiedAiInstructions": "docs/ai/ai-instructions.md", "aiCommandReference": "docs/processes/ai-command-reference.md", "developmentWorkflow": "docs/processes/development-workflow.md", "templateCreationPolicy": "docs/policies/template-creation-policy-override.md", "implementationGuide": "IMPLEMENTATION_GUIDE_FOR_NOVICE.md", "frameworkConfig": ".oa-framework-config.json", "quickStartPrompt": "OA-FRAMEWORK-QUICK-START.md", "aiDiscoveryInstructions": "docs/ai/OA-FRAMEWORK-DISCOVERY-INSTRUCTIONS.md"}, "governanceIndexes": {"adrIndex": "docs/governance/indexes/adr-index.md", "dcrIndex": "docs/governance/indexes/dcr-index.md", "decisionRegister": "docs/governance/indexes/decision-register.md", "dependencyMatrix": "docs/governance/indexes/dependency-matrix.md", "masterGovernanceIndex": "docs/governance/indexes/master-governance-index.md"}, "governanceTemplates": {"discussionTemplate": "docs/governance/templates/discussion-template.md", "adrTemplate": "docs/governance/templates/adr-template.md", "dcrTemplate": "docs/governance/templates/dcr-template.md", "reviewTemplate": "docs/governance/templates/review-template.md"}, "developmentTemplates": {"typescriptHeaderTemplates": "docs/templates/typescript-header-templates.md", "coreComponentTemplates": "docs/templates/core-component-templates.md", "aiCollaborationTemplates": "docs/templates/ai-collaboration-templates.md", "governanceWorkflowTemplates": "docs/templates/governance-workflow-templates.md", "securityValidationTemplates": "docs/templates/security-validation-templates.md", "milestoneExtensionTemplate": "docs/templates/milestone-extension-template.md", "templateComplianceFramework": "docs/templates/template-compliance-framework.md"}, "milestoneStructure": {"foundation": {"M0": "docs/plan/milestone-00-governance-tracking.md", "M0.1": "docs/plan/milestone-00-enhancements-m0.1.md", "M0.2": "docs/plan/milestone-00.2-unified-api-gateway.md", "M0A": "docs/plan/milestone-00a-business-app-gov-ext.md", "M1": "docs/plan/milestone-01-governance-first..md", "M1A": "docs/plan/milestone-01a-foundation-for-m11.md", "M1B": "docs/plan/milestone-01b-bootstrap.md", "M1C": "docs/plan/milestone-01c-business-application-foundation.md"}, "authentication": {"M2": "docs/plan/milestone-02-governance-integrated.md", "M2A": "docs/plan/milestone-02a-application-authentication.md"}, "userExperience": {"M3": "docs/plan/milestone-03-user-dashboard.md", "M4": "docs/plan/milestone-04-admin-panel.md", "M4A": "docs/plan/milestone-04a-administration-interface.md", "M5": "docs/plan/milestone-05-realtime-features.md", "M6": "docs/plan/milestone-06-plugin-system.md"}, "production": {"M7": "docs/plan/milestone-07-production-ready.md", "M7A": "docs/plan/milestone-07a-foundation-for-m11.md", "M7B": "docs/plan/milestone-07b-framework-enterprise-infra.md"}, "enterprise": {"M8": "docs/plan/milestone-08-advanced-governance-consolidated.md", "M11": "docs/plan/Milestone-11-external-database-management.md", "M11A": "docs/plan/milestone-m11a-business-application-registry.md", "M11A-I": "docs/plan/milestone-m11a_i-integration.md", "M11B": "docs/plan/milestone-m11b-resource-inheritance-framework.md"}}, "discoveryPatterns": {"trackingFilePattern": "docs/tracking/.oa-*.json*", "governanceFilePattern": "docs/governance/**/*.md", "milestonePattern": "docs/plan/milestone-*.md", "templatePattern": "templates/**/*.template", "indexPattern": "docs/governance/indexes/*.md", "coreFrameworkPattern": "docs/core/*.md", "processPattern": "docs/processes/*.md", "policyPattern": "docs/policies/*.md"}, "projectStructure": {"architecture": "server-shared-client", "serverDirectory": "server/src/platform", "sharedDirectory": "shared/src", "clientDirectory": "client/src", "templatesDirectory": "templates", "templatePolicy": "on-demand-creation-with-policy-override"}, "systemStatus": {"governanceGateStatus": "ACTIVATED", "trackingSystemsStatus": "ALL_INITIALIZED", "orchestrationDriverStatus": "ENHANCED_V6_3_COORDINATING", "fileOrganizationStatus": "PROPERLY_STRUCTURED", "templatePolicyStatus": "OVERRIDE_ACTIVE", "authorityValidationStatus": "E_Z_CONSULTANCY_VALIDATED", "milestoneStatus": {"M0": "INITIATED", "M0.1": "PLANNED", "M0.2": "PLANNED", "M0A": "PLANNED", "M1": "PLANNED", "M11A-I": "PLANNED"}, "lastSystemCheck": "2025-08-17T00:33:00Z"}, "aiDiscoveryInstructions": {"primaryDiscoveryMethod": "READ_THIS_CONFIG_FILE_FIRST", "quickStartPrompt": "OA Framework: Read .oa-framework-config.json for all paths, then check docs/tracking/ for status.", "quickStartFile": "docs/context/framework/guides/OA-FRAMEWORK-QUICK-START.md", "trackingFilesLocation": "Always check docs/tracking/ directory", "governanceSystemLocation": "Always check docs/governance/ directory structure", "configFileLocation": "This file (.oa-framework-config.json) in project root", "templatePolicyLocation": "docs/policies/template-creation-policy-override.md", "discoverySequence": ["1. Read .oa-framework-config.json for all paths", "2. Check docs/tracking/ for current status", "3. Use trackingFiles paths for specific file locations", "4. Use frameworkPaths for directory navigation", "5. Use discoveryPatterns for file pattern matching", "6. Follow Template Creation Policy Override for implementations"]}, "integrationPoints": {"orchestrationDriver": {"configAware": true, "autoDiscovery": true, "pathResolution": "SMART_PATH_ENABLED", "version": "enhanced-v6.3", "intelligentCoordination": true}, "trackingSystems": {"configIntegrated": true, "autoLocationDetection": true, "fileLocationCaching": true, "realTimeMonitoring": true, "comprehensiveAnalytics": true}, "governanceSystem": {"configDriven": true, "pathValidation": true, "structureVerification": true, "authorityValidation": "e-z-consultancy", "cryptographicIntegrity": "SHA256-protected"}, "templateSystem": {"policyOverride": "active", "onDemandCreation": true, "milestonePathIgnoring": true, "standardsInheritance": "development-standards-v21", "projectStructureEnforcement": "server-shared-client"}}}