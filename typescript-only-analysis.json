{"total": 212, "byPriority": {"critical": 76, "high": 105, "medium": 30, "low": 1}, "byHeaderStatus": {"minimal-header": 15, "v2.0-or-older": 24, "v2.1": 172, "no-header": 1}, "byTier": {"server": 122, "shared": 89, "docs": 1}, "largeFiles": 109, "files": [{"path": "./server/src/platform/integration/core-bridge/RealtimeEventCoordinator.ts", "headerStatus": "minimal-header", "lines": 2657, "size": 76788, "tier": "server", "category": "typescript", "priority": "critical"}, {"path": "./server/src/platform/integration/core-bridge/AuthorityComplianceMonitorBridge.ts", "headerStatus": "minimal-header", "lines": 2324, "size": 76330, "tier": "server", "category": "typescript", "priority": "critical"}, {"path": "./server/src/platform/integration/core-bridge/CrossReferenceValidationBridge.ts", "headerStatus": "v2.0-or-older", "lines": 1877, "size": 62207, "tier": "server", "category": "typescript", "priority": "critical"}, {"path": "./server/src/platform/integration/core-bridge/GovernanceTrackingBridge.ts", "headerStatus": "v2.1", "lines": 3087, "size": 98108, "tier": "server", "category": "typescript", "priority": "critical"}, {"path": "./server/src/platform/tracking/advanced-data/SmartPathResolutionSystem.ts", "headerStatus": "v2.1", "lines": 924, "size": 30715, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/tracking/advanced-data/ContextAuthorityProtocol.ts", "headerStatus": "v2.1", "lines": 1431, "size": 47746, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/tracking/advanced-data/CrossReferenceValidationEngine.ts", "headerStatus": "v2.1", "lines": 1259, "size": 40200, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/tracking/advanced-data/OrchestrationCoordinator.ts", "headerStatus": "v2.1", "lines": 1280, "size": 41206, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/tracking/core-data/GovernanceLogTracker.ts", "headerStatus": "v2.0-or-older", "lines": 1847, "size": 62645, "tier": "server", "category": "typescript", "priority": "critical"}, {"path": "./server/src/platform/tracking/core-data/ImplementationProgressTracker.ts", "headerStatus": "v2.0-or-older", "lines": 1484, "size": 51532, "tier": "server", "category": "typescript", "priority": "critical"}, {"path": "./server/src/platform/tracking/core-data/base/BaseTrackingService.ts", "headerStatus": "v2.0-or-older", "lines": 1977, "size": 65416, "tier": "server", "category": "typescript", "priority": "critical"}, {"path": "./server/src/platform/tracking/core-data/AnalyticsCacheManager.ts", "headerStatus": "v2.0-or-older", "lines": 2030, "size": 64684, "tier": "server", "category": "typescript", "priority": "critical"}, {"path": "./server/src/platform/tracking/core-data/SessionLogTracker.ts", "headerStatus": "v2.0-or-older", "lines": 2818, "size": 91291, "tier": "server", "category": "typescript", "priority": "critical"}, {"path": "./server/src/platform/tracking/core-utils/TrackingUtilities.ts", "headerStatus": "v2.1", "lines": 1031, "size": 32553, "tier": "server", "category": "typescript", "priority": "critical"}, {"path": "./server/src/platform/tracking/core-managers/TrackingManager.ts", "headerStatus": "v2.1", "lines": 1082, "size": 36717, "tier": "server", "category": "typescript", "priority": "critical"}, {"path": "./server/src/platform/tracking/core-managers/DashboardManager.ts", "headerStatus": "v2.1", "lines": 1332, "size": 43416, "tier": "server", "category": "typescript", "priority": "critical"}, {"path": "./server/src/platform/tracking/core-managers/FileManager.ts", "headerStatus": "v2.1", "lines": 880, "size": 26085, "tier": "server", "category": "typescript", "priority": "critical"}, {"path": "./server/src/platform/tracking/core-managers/RealTimeManager.ts", "headerStatus": "v2.1", "lines": 1735, "size": 58807, "tier": "server", "category": "typescript", "priority": "critical"}, {"path": "./server/src/platform/tracking/core-trackers/AuthorityTrackingService.ts", "headerStatus": "v2.1", "lines": 465, "size": 15801, "tier": "server", "category": "typescript", "priority": "critical"}, {"path": "./server/src/platform/tracking/core-trackers/SessionTrackingCore.ts", "headerStatus": "v2.1", "lines": 735, "size": 22445, "tier": "server", "category": "typescript", "priority": "critical"}, {"path": "./server/src/platform/tracking/core-trackers/SessionTrackingUtils.ts", "headerStatus": "v2.1", "lines": 533, "size": 16626, "tier": "server", "category": "typescript", "priority": "critical"}, {"path": "./server/src/platform/tracking/core-trackers/security/ISecurityEnforcement.ts", "headerStatus": "minimal-header", "lines": 142, "size": 3581, "tier": "server", "category": "typescript", "priority": "critical"}, {"path": "./server/src/platform/tracking/core-trackers/security/SecurityConfig.ts", "headerStatus": "minimal-header", "lines": 238, "size": 5446, "tier": "server", "category": "typescript", "priority": "critical"}, {"path": "./server/src/platform/tracking/core-trackers/security/SecurityEnforcementLayer.ts", "headerStatus": "minimal-header", "lines": 364, "size": 12045, "tier": "server", "category": "typescript", "priority": "critical"}, {"path": "./server/src/platform/tracking/core-trackers/CrossReferenceTrackingEngine.ts", "headerStatus": "v2.1", "lines": 381, "size": 13058, "tier": "server", "category": "typescript", "priority": "critical"}, {"path": "./server/src/platform/tracking/core-trackers/SessionTrackingAudit.ts", "headerStatus": "v2.1", "lines": 554, "size": 17375, "tier": "server", "category": "typescript", "priority": "critical"}, {"path": "./server/src/platform/tracking/core-trackers/OrchestrationTrackingSystem.ts", "headerStatus": "v2.1", "lines": 457, "size": 16915, "tier": "server", "category": "typescript", "priority": "critical"}, {"path": "./server/src/platform/tracking/core-trackers/ProgressTrackingEngine.ts", "headerStatus": "v2.1", "lines": 978, "size": 30660, "tier": "server", "category": "typescript", "priority": "critical"}, {"path": "./server/src/platform/tracking/core-trackers/SessionTrackingRealtime.ts", "headerStatus": "v2.1", "lines": 386, "size": 11509, "tier": "server", "category": "typescript", "priority": "critical"}, {"path": "./server/src/platform/tracking/core-trackers/SmartPathTrackingSystem.ts", "headerStatus": "v2.1", "lines": 375, "size": 11193, "tier": "server", "category": "typescript", "priority": "critical"}, {"path": "./server/src/platform/governance/analytics-engines/GovernanceRuleReportingEngineFactory.ts", "headerStatus": "minimal-header", "lines": 634, "size": 19418, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/analytics-engines/GovernanceRuleInsightsGeneratorFactory.ts", "headerStatus": "v2.1", "lines": 687, "size": 22491, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/analytics-engines/GovernanceRuleAnalyticsEngineFactory.ts", "headerStatus": "v2.1", "lines": 594, "size": 17734, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/analytics-engines/GovernanceRuleAnalyticsEngine.ts", "headerStatus": "v2.1", "lines": 1434, "size": 46914, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/analytics-engines/index.ts", "headerStatus": "v2.1", "lines": 275, "size": 8825, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/analytics-engines/GovernanceRuleOptimizationEngineFactory.ts", "headerStatus": "v2.1", "lines": 603, "size": 19503, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/analytics-engines/GovernanceRuleReportingEngine.ts", "headerStatus": "minimal-header", "lines": 1174, "size": 38006, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/analytics-engines/GovernanceRuleInsightsGenerator.ts", "headerStatus": "v2.1", "lines": 1074, "size": 31802, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/analytics-engines/GovernanceRuleOptimizationEngine.ts", "headerStatus": "v2.1", "lines": 1578, "size": 51677, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/security-management/RuleSecurityManager.ts", "headerStatus": "v2.1", "lines": 807, "size": 25336, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/security-management/RuleAuditLogger.ts", "headerStatus": "v2.1", "lines": 654, "size": 20507, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/security-management/RuleSecurityFramework.ts", "headerStatus": "v2.1", "lines": 738, "size": 23720, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/security-management/RuleIntegrityValidator.ts", "headerStatus": "v2.1", "lines": 719, "size": 23164, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/management-configuration/GovernanceRuleTemplateEngine.ts", "headerStatus": "v2.1", "lines": 1627, "size": 50352, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/management-configuration/GovernanceRuleSecurityPolicy.ts", "headerStatus": "v2.1", "lines": 536, "size": 17116, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/management-configuration/GovernanceRuleConfigurationManager.ts", "headerStatus": "v2.1", "lines": 1169, "size": 35912, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/management-configuration/GovernanceRuleDocumentationGenerator.ts", "headerStatus": "v2.1", "lines": 1222, "size": 42587, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/management-configuration/GovernanceRuleInputValidator.ts", "headerStatus": "v2.1", "lines": 1201, "size": 40591, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/management-configuration/GovernanceRuleTemplateSecurity.ts", "headerStatus": "v2.1", "lines": 823, "size": 26276, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/management-configuration/GovernanceRuleEnvironmentManager.ts", "headerStatus": "v2.1", "lines": 1172, "size": 38544, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/reporting-infrastructure/GovernanceRuleComplianceReporter.ts", "headerStatus": "v2.1", "lines": 1773, "size": 58874, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/reporting-infrastructure/GovernanceRuleDashboardGeneratorFactory.ts", "headerStatus": "minimal-header", "lines": 357, "size": 12258, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/reporting-infrastructure/GovernanceRuleAlertManager.ts", "headerStatus": "v2.1", "lines": 2173, "size": 71900, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/reporting-infrastructure/index.ts", "headerStatus": "minimal-header", "lines": 198, "size": 6159, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/reporting-infrastructure/GovernanceRuleComplianceReporterFactory.ts", "headerStatus": "v2.1", "lines": 654, "size": 21756, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/reporting-infrastructure/GovernanceRuleReportScheduler.ts", "headerStatus": "v2.1", "lines": 2032, "size": 67335, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/reporting-infrastructure/GovernanceRuleDashboardGenerator.ts", "headerStatus": "minimal-header", "lines": 1263, "size": 39902, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/reporting-infrastructure/GovernanceRuleReportSchedulerFactory.ts", "headerStatus": "v2.1", "lines": 684, "size": 23120, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/reporting-infrastructure/GovernanceRuleAlertManagerFactory.ts", "headerStatus": "v2.1", "lines": 400, "size": 12981, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/rule-management/RuleDependencyGraphAnalyzer.ts", "headerStatus": "v2.0-or-older", "lines": 1558, "size": 46442, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/rule-management/RuleExecutionResultProcessor.ts", "headerStatus": "v2.0-or-older", "lines": 1750, "size": 52617, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/rule-management/core/GovernanceRuleExecutionContext.ts", "headerStatus": "v2.0-or-older", "lines": 1060, "size": 33594, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/rule-management/core/GovernanceRuleEngineCore.ts", "headerStatus": "v2.0-or-older", "lines": 1337, "size": 41984, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/rule-management/compliance/GovernanceComplianceChecker.ts", "headerStatus": "v2.0-or-older", "lines": 1143, "size": 36617, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/rule-management/compliance/GovernanceAuthorityValidator.ts", "headerStatus": "v2.0-or-older", "lines": 1573, "size": 51182, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/rule-management/infrastructure/GovernanceRuleAuditLogger.ts", "headerStatus": "v2.0-or-older", "lines": 1480, "size": 47162, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/rule-management/RuleConflictResolutionEngine.ts", "headerStatus": "v2.0-or-older", "lines": 1760, "size": 54213, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/rule-management/RuleExecutionContextManager.ts", "headerStatus": "v2.0-or-older", "lines": 1546, "size": 46905, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/continuity-backup/GovernanceRuleRecoveryManager.ts", "headerStatus": "v2.1", "lines": 1099, "size": 33116, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/continuity-backup/GovernanceRuleDisasterRecovery.ts", "headerStatus": "v2.1", "lines": 1387, "size": 41589, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/continuity-backup/GovernanceRuleBackupManagerContinuity.ts", "headerStatus": "v2.1", "lines": 1480, "size": 43120, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/continuity-backup/GovernanceRuleFailoverManager.ts", "headerStatus": "v2.1", "lines": 1518, "size": 44559, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/enterprise-frameworks/GovernanceRuleEnterpriseFramework.ts", "headerStatus": "v2.1", "lines": 1528, "size": 42373, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/enterprise-frameworks/GovernanceRuleIntegrationFramework.ts", "headerStatus": "v2.1", "lines": 729, "size": 24361, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/enterprise-frameworks/GovernanceRuleManagementFramework.ts", "headerStatus": "v2.1", "lines": 1122, "size": 34044, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/enterprise-frameworks/GovernanceRuleGovernanceFramework.ts", "headerStatus": "v2.1", "lines": 1490, "size": 42905, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/automation-processing/GovernanceRuleNotificationSystemAutomation.ts", "headerStatus": "v2.1", "lines": 1216, "size": 41818, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/automation-processing/factories/RuleAuditLoggerFactory.ts", "headerStatus": "v2.1", "lines": 146, "size": 5944, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/automation-processing/GovernanceRuleTransformationEngine.ts", "headerStatus": "v2.1", "lines": 1529, "size": 50859, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/automation-processing/errors/ProcessingErrors.ts", "headerStatus": "v2.1", "lines": 265, "size": 9694, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/automation-processing/GovernanceRuleMaintenanceScheduler.ts", "headerStatus": "v2.1", "lines": 1081, "size": 36343, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/automation-processing/GovernanceRuleEventManager.ts", "headerStatus": "v2.1", "lines": 1395, "size": 46019, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/automation-engines/governance-rule-scheduling-engine.ts", "headerStatus": "v2.1", "lines": 901, "size": 30398, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/automation-engines/governance-rule-workflow-engine.ts", "headerStatus": "v2.1", "lines": 777, "size": 24484, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/automation-engines/governance-rule-automation-engine.ts", "headerStatus": "v2.1", "lines": 810, "size": 27379, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/automation-engines/governance-rule-processing-engine.ts", "headerStatus": "v2.1", "lines": 954, "size": 32664, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/compliance-infrastructure/GovernanceRuleComplianceChecker.ts", "headerStatus": "v2.1", "lines": 1110, "size": 34744, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/compliance-infrastructure/GovernanceRuleComplianceFramework.ts", "headerStatus": "v2.1", "lines": 1161, "size": 35362, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/compliance-infrastructure/GovernanceRuleTestingFramework.ts", "headerStatus": "minimal-header", "lines": 1059, "size": 35306, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/compliance-infrastructure/GovernanceRuleQualityFramework.ts", "headerStatus": "v2.1", "lines": 1191, "size": 37246, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/compliance-infrastructure/index.ts", "headerStatus": "v2.1", "lines": 99, "size": 3515, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/performance-management/analytics/RulePerformanceProfiler.ts", "headerStatus": "v2.1", "lines": 926, "size": 28776, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/performance-management/monitoring/RuleNotificationSystem.ts", "headerStatus": "v2.1", "lines": 930, "size": 30555, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/performance-management/monitoring/RuleHealthChecker.ts", "headerStatus": "v2.1", "lines": 1108, "size": 34937, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/performance-management/monitoring/RuleMonitoringSystem.ts", "headerStatus": "v2.1", "lines": 1039, "size": 32125, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/performance-management/monitoring/RuleMetricsCollector.ts", "headerStatus": "v2.1", "lines": 1183, "size": 40290, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/performance-management/optimization/RulePerformanceOptimizer.ts", "headerStatus": "v2.1", "lines": 1324, "size": 40747, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/performance-management/cache/RuleResourceManager.ts", "headerStatus": "v2.1", "lines": 1707, "size": 53217, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/governance/performance-management/cache/RuleCacheManager.ts", "headerStatus": "v2.1", "lines": 1386, "size": 43735, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/documentation/system-docs/GovernanceSystemDocGenerator.ts", "headerStatus": "v2.1", "lines": 1426, "size": 49518, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/documentation/system-docs/IntegrationDocCompiler.ts", "headerStatus": "v2.1", "lines": 1875, "size": 65213, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/documentation/system-docs/MemorySafetyDocBuilder.ts", "headerStatus": "v2.1", "lines": 1632, "size": 58854, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/documentation/system-docs/TroubleshootingGuideAutomation.ts", "headerStatus": "minimal-header", "lines": 2373, "size": 79689, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/documentation/system-docs/TrackingSystemGuideGenerator.ts", "headerStatus": "v2.1", "lines": 2430, "size": 79517, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/documentation/training-materials/GovernanceAdminTrainingSystem.ts", "headerStatus": "v2.0-or-older", "lines": 1573, "size": 54833, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/documentation/training-materials/BestPracticesDocEngine.ts", "headerStatus": "v2.1", "lines": 2058, "size": 67312, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/documentation/training-materials/CommonWorkflowsGuideGenerator.ts", "headerStatus": "minimal-header", "lines": 1711, "size": 56969, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/documentation/training-materials/TrackingDashboardTrainingPortal.ts", "headerStatus": "minimal-header", "lines": 2204, "size": 77286, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/platform/documentation/training-materials/MemorySafetyPracticesGuide.ts", "headerStatus": "minimal-header", "lines": 1625, "size": 57090, "tier": "server", "category": "typescript", "priority": "high"}, {"path": "./server/src/types/dependency-types.ts", "headerStatus": "v2.1", "lines": 61, "size": 2107, "tier": "server", "category": "typescript", "priority": "medium"}, {"path": "./server/src/errors/security-errors.ts", "headerStatus": "v2.1", "lines": 78, "size": 2574, "tier": "server", "category": "typescript", "priority": "medium"}, {"path": "./server/src/interfaces/storage/storage-interfaces.ts", "headerStatus": "v2.1", "lines": 103, "size": 3072, "tier": "server", "category": "typescript", "priority": "medium"}, {"path": "./server/src/interfaces/configuration/configuration-interfaces.ts", "headerStatus": "v2.1", "lines": 72, "size": 2310, "tier": "server", "category": "typescript", "priority": "medium"}, {"path": "./server/src/interfaces/security/security-interfaces.ts", "headerStatus": "v2.1", "lines": 138, "size": 3873, "tier": "server", "category": "typescript", "priority": "medium"}, {"path": "./server/src/interfaces/security/crypto-interfaces.ts", "headerStatus": "v2.1", "lines": 81, "size": 2408, "tier": "server", "category": "typescript", "priority": "medium"}, {"path": "./server/src/interfaces/security/audit-interfaces.ts", "headerStatus": "v2.1", "lines": 181, "size": 4607, "tier": "server", "category": "typescript", "priority": "medium"}, {"path": "./server/src/interfaces/security/hash-interfaces.ts", "headerStatus": "v2.1", "lines": 75, "size": 2182, "tier": "server", "category": "typescript", "priority": "medium"}, {"path": "./server/src/interfaces/security/integrity-interfaces.ts", "headerStatus": "v2.1", "lines": 161, "size": 4445, "tier": "server", "category": "typescript", "priority": "medium"}, {"path": "./server/src/interfaces/security/authorization-interfaces.ts", "headerStatus": "v2.1", "lines": 69, "size": 2135, "tier": "server", "category": "typescript", "priority": "medium"}, {"path": "./server/src/interfaces/security/framework-interfaces.ts", "headerStatus": "v2.1", "lines": 116, "size": 3455, "tier": "server", "category": "typescript", "priority": "medium"}, {"path": "./server/src/interfaces/logging/logging-interfaces.ts", "headerStatus": "v2.1", "lines": 89, "size": 2505, "tier": "server", "category": "typescript", "priority": "medium"}, {"path": "./server/src/interfaces/monitoring/monitoring-interfaces.ts", "headerStatus": "v2.1", "lines": 93, "size": 2601, "tier": "server", "category": "typescript", "priority": "medium"}, {"path": "./shared/src/types/platform/tracking/tracking-types.ts", "headerStatus": "v2.1", "lines": 113, "size": 4320, "tier": "shared", "category": "typescript", "priority": "high"}, {"path": "./shared/src/types/platform/tracking/utilities/workflow-types.ts", "headerStatus": "v2.1", "lines": 92, "size": 2857, "tier": "shared", "category": "typescript", "priority": "high"}, {"path": "./shared/src/types/platform/tracking/utilities/metrics-types.ts", "headerStatus": "v2.1", "lines": 81, "size": 2505, "tier": "shared", "category": "typescript", "priority": "high"}, {"path": "./shared/src/types/platform/tracking/utilities/error-types.ts", "headerStatus": "v2.1", "lines": 91, "size": 2772, "tier": "shared", "category": "typescript", "priority": "high"}, {"path": "./shared/src/types/platform/tracking/core/base-types.ts", "headerStatus": "v2.1", "lines": 276, "size": 6303, "tier": "shared", "category": "typescript", "priority": "high"}, {"path": "./shared/src/types/platform/tracking/core/tracking-data-types.ts", "headerStatus": "v2.1", "lines": 511, "size": 11953, "tier": "shared", "category": "typescript", "priority": "high"}, {"path": "./shared/src/types/platform/tracking/core/tracking-service-types.ts", "headerStatus": "v2.1", "lines": 338, "size": 8975, "tier": "shared", "category": "typescript", "priority": "high"}, {"path": "./shared/src/types/platform/tracking/core/tracking-config-types.ts", "headerStatus": "v2.1", "lines": 275, "size": 6821, "tier": "shared", "category": "typescript", "priority": "high"}, {"path": "./shared/src/types/platform/tracking/index.ts", "headerStatus": "v2.1", "lines": 273, "size": 8063, "tier": "shared", "category": "typescript", "priority": "high"}, {"path": "./shared/src/types/platform/governance/notification-types.ts", "headerStatus": "v2.1", "lines": 151, "size": 4272, "tier": "shared", "category": "typescript", "priority": "high"}, {"path": "./shared/src/types/platform/governance/management-configuration/environment-manager-types.ts", "headerStatus": "v2.1", "lines": 1031, "size": 22371, "tier": "shared", "category": "typescript", "priority": "high"}, {"path": "./shared/src/types/platform/governance/management-configuration/tracking-system-guide-generator-types.ts", "headerStatus": "v2.1", "lines": 1060, "size": 24035, "tier": "shared", "category": "typescript", "priority": "high"}, {"path": "./shared/src/types/platform/governance/management-configuration/documentation-generator-types.ts", "headerStatus": "v2.1", "lines": 3805, "size": 86651, "tier": "shared", "category": "typescript", "priority": "high"}, {"path": "./shared/src/types/platform/governance/resource-interfaces.ts", "headerStatus": "v2.1", "lines": 111, "size": 3675, "tier": "shared", "category": "typescript", "priority": "high"}, {"path": "./shared/src/types/platform/governance/security-types.ts", "headerStatus": "v2.1", "lines": 381, "size": 10944, "tier": "shared", "category": "typescript", "priority": "high"}, {"path": "./shared/src/types/platform/governance/rule-management-types.ts", "headerStatus": "v2.1", "lines": 2487, "size": 53575, "tier": "shared", "category": "typescript", "priority": "high"}, {"path": "./shared/src/types/platform/governance/automation-processing-types.ts", "headerStatus": "v2.1", "lines": 1998, "size": 56518, "tier": "shared", "category": "typescript", "priority": "high"}, {"path": "./shared/src/types/platform/governance/governance-interfaces.ts", "headerStatus": "v2.1", "lines": 1203, "size": 33081, "tier": "shared", "category": "typescript", "priority": "high"}, {"path": "./shared/src/types/platform/governance/automation-engines/workflow-engines-types.ts", "headerStatus": "v2.1", "lines": 779, "size": 26201, "tier": "shared", "category": "typescript", "priority": "high"}, {"path": "./shared/src/types/platform/governance/governance-types.ts", "headerStatus": "v2.1", "lines": 1287, "size": 30855, "tier": "shared", "category": "typescript", "priority": "high"}, {"path": "./shared/src/types/tracking/tracking-management-types.ts", "headerStatus": "v2.1", "lines": 523, "size": 12540, "tier": "shared", "category": "typescript", "priority": "high"}, {"path": "./shared/src/types/tracking/core-types.ts", "headerStatus": "v2.1", "lines": 248, "size": 6095, "tier": "shared", "category": "typescript", "priority": "high"}, {"path": "./shared/src/base/EventHandlerRegistry.ts", "headerStatus": "v2.0-or-older", "lines": 577, "size": 20827, "tier": "shared", "category": "typescript", "priority": "medium"}, {"path": "./shared/src/base/TimerCoordinationService.ts", "headerStatus": "v2.0-or-older", "lines": 713, "size": 25945, "tier": "shared", "category": "typescript", "priority": "medium"}, {"path": "./shared/src/base/event-handler-registry/types/EventHandlerEnhancedTypes.ts", "headerStatus": "v2.1", "lines": 231, "size": 7675, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/event-handler-registry/types/EventTypes.ts", "headerStatus": "v2.1", "lines": 349, "size": 11508, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/event-handler-registry/types/EventConfiguration.ts", "headerStatus": "v2.1", "lines": 396, "size": 12996, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/event-handler-registry/modules/DeduplicationEngine.ts", "headerStatus": "v2.1", "lines": 587, "size": 21868, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/event-handler-registry/modules/MetricsManager.ts", "headerStatus": "v2.1", "lines": 331, "size": 10980, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/event-handler-registry/modules/ComplianceManager.ts", "headerStatus": "v2.1", "lines": 374, "size": 12567, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/event-handler-registry/modules/MiddlewareManager.ts", "headerStatus": "v2.1", "lines": 457, "size": 15557, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/event-handler-registry/modules/EventBuffering.ts", "headerStatus": "v2.1", "lines": 857, "size": 29239, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/event-handler-registry/modules/EventUtilities.ts", "headerStatus": "v2.1", "lines": 314, "size": 10551, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/event-handler-registry/modules/EventEmissionSystem.ts", "headerStatus": "v2.1", "lines": 385, "size": 13021, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/MemorySafeResourceManager.ts", "headerStatus": "v2.0-or-older", "lines": 1021, "size": 36262, "tier": "shared", "category": "typescript", "priority": "medium"}, {"path": "./shared/src/base/AtomicCircularBuffer.ts", "headerStatus": "v2.0-or-older", "lines": 558, "size": 20009, "tier": "shared", "category": "typescript", "priority": "medium"}, {"path": "./shared/src/base/atomic-circular-buffer-enhanced/modules/BufferUtilities.ts", "headerStatus": "v2.1", "lines": 459, "size": 14787, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/atomic-circular-buffer-enhanced/modules/BufferStrategyManager.ts", "headerStatus": "v2.1", "lines": 652, "size": 22536, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/atomic-circular-buffer-enhanced/modules/BufferConfigurationManager.ts", "headerStatus": "v2.1", "lines": 499, "size": 17485, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/atomic-circular-buffer-enhanced/modules/BufferPersistenceManager.ts", "headerStatus": "v2.1", "lines": 534, "size": 17852, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/atomic-circular-buffer-enhanced/modules/BufferOperationsManager.ts", "headerStatus": "v2.1", "lines": 470, "size": 15682, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/atomic-circular-buffer-enhanced/modules/BufferAnalyticsEngine.ts", "headerStatus": "v2.1", "lines": 633, "size": 22407, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/utils/ResilientTiming.ts", "headerStatus": "v2.0-or-older", "lines": 371, "size": 12655, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/LoggingMixin.ts", "headerStatus": "v2.0-or-older", "lines": 236, "size": 9310, "tier": "shared", "category": "typescript", "priority": "medium"}, {"path": "./shared/src/base/timer-coordination/types/TimerTypes.ts", "headerStatus": "v2.1", "lines": 385, "size": 12499, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/timer-coordination/modules/TimerCoordinationPatterns.ts", "headerStatus": "v2.1", "lines": 796, "size": 28031, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/timer-coordination/modules/TimerConfiguration.ts", "headerStatus": "v2.1", "lines": 358, "size": 11572, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/timer-coordination/modules/PhaseIntegration.ts", "headerStatus": "v2.1", "lines": 407, "size": 15285, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/timer-coordination/modules/TimerUtilities.ts", "headerStatus": "v2.1", "lines": 541, "size": 20205, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/timer-coordination/modules/AdvancedScheduler.ts", "headerStatus": "v2.1", "lines": 719, "size": 25285, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/timer-coordination/modules/TimerPoolManager.ts", "headerStatus": "v2.1", "lines": 584, "size": 20563, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/memory-safety-manager/modules/SystemCoordinationManager.ts", "headerStatus": "v2.1", "lines": 644, "size": 20901, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/memory-safety-manager/modules/SystemStateManager.ts", "headerStatus": "v2.1", "lines": 597, "size": 18305, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/memory-safety-manager/modules/ComponentDiscoveryManager.ts", "headerStatus": "v2.1", "lines": 633, "size": 21203, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/memory-safety-manager/modules/EnhancedMetricsCollector.ts", "headerStatus": "v2.1", "lines": 595, "size": 18629, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/memory-safety-manager/modules/ComponentIntegrationEngine.ts", "headerStatus": "v2.1", "lines": 342, "size": 11604, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/memory-safety-manager/modules/EnhancedConfigurationManager.ts", "headerStatus": "v2.1", "lines": 432, "size": 14713, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/MemorySafetyManager.ts", "headerStatus": "v2.0-or-older", "lines": 964, "size": 33186, "tier": "shared", "category": "typescript", "priority": "medium"}, {"path": "./shared/src/base/CleanupCoordinatorEnhanced.ts", "headerStatus": "v2.0-or-older", "lines": 1211, "size": 41773, "tier": "shared", "category": "typescript", "priority": "medium"}, {"path": "./shared/src/base/cleanup-coordinator-enhanced/modules/CleanupConfiguration.ts", "headerStatus": "v2.1", "lines": 459, "size": 15716, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/cleanup-coordinator-enhanced/modules/RollbackSnapshots.ts", "headerStatus": "v2.1", "lines": 312, "size": 11147, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/cleanup-coordinator-enhanced/modules/CleanupTemplateManager.ts", "headerStatus": "v2.1", "lines": 677, "size": 24545, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/cleanup-coordinator-enhanced/modules/CleanupUtilities.ts", "headerStatus": "v2.1", "lines": 206, "size": 6280, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/cleanup-coordinator-enhanced/modules/RollbackUtilities.ts", "headerStatus": "v2.1", "lines": 339, "size": 12400, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/cleanup-coordinator-enhanced/modules/SystemOrchestrator.ts", "headerStatus": "v2.1", "lines": 733, "size": 25398, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/cleanup-coordinator-enhanced/modules/DependencyResolver.ts", "headerStatus": "v2.1", "lines": 746, "size": 24597, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/cleanup-coordinator-enhanced/modules/UtilityPerformance.ts", "headerStatus": "v2.1", "lines": 633, "size": 22601, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/cleanup-coordinator-enhanced/modules/UtilityExecution.ts", "headerStatus": "v2.1", "lines": 364, "size": 12046, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/cleanup-coordinator-enhanced/modules/TemplateDependencies.ts", "headerStatus": "v2.1", "lines": 642, "size": 18531, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/cleanup-coordinator-enhanced/modules/TemplateValidation.ts", "headerStatus": "v2.1", "lines": 967, "size": 33691, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/cleanup-coordinator-enhanced/modules/HealthStatusManager.ts", "headerStatus": "v2.1", "lines": 387, "size": 13213, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/cleanup-coordinator-enhanced/modules/TimingInfrastructureManager.ts", "headerStatus": "v2.1", "lines": 360, "size": 11888, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/cleanup-coordinator-enhanced/modules/UtilityValidation.ts", "headerStatus": "v2.1", "lines": 435, "size": 14521, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/cleanup-coordinator-enhanced/modules/UtilityAnalysis.ts", "headerStatus": "v2.1", "lines": 250, "size": 8667, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/cleanup-coordinator-enhanced/modules/AsyncErrorHandler.ts", "headerStatus": "v2.1", "lines": 355, "size": 11204, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/cleanup-coordinator-enhanced/modules/TemplateWorkflows.ts", "headerStatus": "v2.1", "lines": 1058, "size": 36183, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/cleanup-coordinator-enhanced/modules/RollbackManager.ts", "headerStatus": "v2.1", "lines": 848, "size": 29139, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/cleanup-coordinator-enhanced/modules/InitializationManager.ts", "headerStatus": "v2.1", "lines": 326, "size": 11735, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/base/cleanup-coordinator-enhanced/modules/OperationExecutionManager.ts", "headerStatus": "v2.1", "lines": 794, "size": 28520, "tier": "shared", "category": "typescript", "priority": "critical"}, {"path": "./shared/src/interfaces/tracking/core-interfaces.ts", "headerStatus": "v2.1", "lines": 464, "size": 11348, "tier": "shared", "category": "typescript", "priority": "medium"}, {"path": "./shared/src/interfaces/tracking/notification-interfaces.ts", "headerStatus": "v2.1", "lines": 115, "size": 3950, "tier": "shared", "category": "typescript", "priority": "medium"}, {"path": "./shared/src/interfaces/tracking/tracking-interfaces.ts", "headerStatus": "v2.1", "lines": 280, "size": 6779, "tier": "shared", "category": "typescript", "priority": "medium"}, {"path": "./shared/src/interfaces/governance/management-configuration/governance-rule-environment-manager.ts", "headerStatus": "v2.1", "lines": 350, "size": 8585, "tier": "shared", "category": "typescript", "priority": "medium"}, {"path": "./shared/src/interfaces/governance/management-configuration/governance-rule-documentation-generator.ts", "headerStatus": "v2.1", "lines": 3737, "size": 92172, "tier": "shared", "category": "typescript", "priority": "medium"}, {"path": "./shared/src/interfaces/governance/management-configuration/tracking-system-guide-generator.ts", "headerStatus": "v2.1", "lines": 1289, "size": 30938, "tier": "shared", "category": "typescript", "priority": "medium"}, {"path": "./shared/src/constants/platform/tracking/environment-constants-calculator.ts", "headerStatus": "v2.1", "lines": 735, "size": 26736, "tier": "shared", "category": "typescript", "priority": "medium"}, {"path": "./shared/src/constants/platform/tracking/tracking-constants.ts", "headerStatus": "v2.1", "lines": 359, "size": 11430, "tier": "shared", "category": "typescript", "priority": "medium"}, {"path": "./shared/src/constants/platform/tracking/tracking-constants-enhanced.ts", "headerStatus": "v2.1", "lines": 734, "size": 22290, "tier": "shared", "category": "typescript", "priority": "medium"}, {"path": "./shared/src/constants/tracking/tracking-management-constants.ts", "headerStatus": "v2.1", "lines": 395, "size": 10206, "tier": "shared", "category": "typescript", "priority": "medium"}, {"path": "./docs/lessons/templates/catch-block-coverage.template.ts", "headerStatus": "no-header", "lines": 208, "size": 8127, "tier": "docs", "category": "typescript", "priority": "low"}]}