#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Get all TypeScript and documentation files (excluding tests)
function getAllFiles() {
  try {
    const command = `find ./server ./shared ./client ./docs -name "*.ts" -o -name "*.js" -o -name "*.md" | grep -v "__tests__" | grep -v ".test." | grep -v ".spec."`;
    const output = execSync(command, { encoding: 'utf8' });
    return output.trim().split('\n').filter(file => file.length > 0);
  } catch (error) {
    console.error('Error getting files:', error.message);
    return [];
  }
}

// Analyze header format of a file
function analyzeHeaderFormat(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n');
    const firstLines = lines.slice(0, 100).join('\n');
    
    // Check for v2.3 format
    if (firstLines.includes('OA FRAMEWORK - TYPESCRIPT SOURCE FILE') || 
        firstLines.includes('OA FRAMEWORK - DOCUMENTATION FILE')) {
      return 'v2.3';
    }
    
    // Check for v2.2 format (if exists)
    if (firstLines.includes('AUTHORITY-DRIVEN GOVERNANCE (v2.2)')) {
      return 'v2.2';
    }
    
    // Check for v2.1 format
    if (firstLines.includes('AUTHORITY-DRIVEN GOVERNANCE (v2.1)')) {
      return 'v2.1';
    }
    
    // Check for older formats
    if (firstLines.includes('/**') && (
        firstLines.includes('@file') || 
        firstLines.includes('@component') ||
        firstLines.includes('AUTHORITY-DRIVEN GOVERNANCE')
    )) {
      return 'v2.0-or-older';
    }
    
    // Check for minimal headers
    if (firstLines.includes('/**') && firstLines.includes('*/')) {
      return 'minimal-header';
    }
    
    // No header
    return 'no-header';
  } catch (error) {
    return 'error-reading';
  }
}

// Get file size and line count
function getFileStats(filePath) {
  try {
    const stats = fs.statSync(filePath);
    const content = fs.readFileSync(filePath, 'utf8');
    const lineCount = content.split('\n').length;
    return {
      size: stats.size,
      lines: lineCount
    };
  } catch (error) {
    return { size: 0, lines: 0 };
  }
}

// Categorize file by directory and type
function categorizeFile(filePath) {
  const ext = path.extname(filePath);
  const dir = path.dirname(filePath);
  
  let tier = 'unknown';
  let category = 'unknown';
  let priority = 'low';
  
  if (dir.includes('/server/')) {
    tier = 'server';
    if (dir.includes('/core-')) priority = 'critical';
    else if (dir.includes('/platform/')) priority = 'high';
    else priority = 'medium';
  } else if (dir.includes('/shared/')) {
    tier = 'shared';
    if (dir.includes('/base/')) priority = 'critical';
    else if (dir.includes('/types/')) priority = 'high';
    else priority = 'medium';
  } else if (dir.includes('/client/')) {
    tier = 'client';
    priority = 'medium';
  } else if (dir.includes('/docs/')) {
    tier = 'docs';
    priority = 'low';
  }
  
  if (ext === '.ts') category = 'typescript';
  else if (ext === '.js') category = 'javascript';
  else if (ext === '.md') category = 'documentation';
  
  return { tier, category, priority };
}

// Main analysis
function analyzeRepository() {
  console.log('🔍 Analyzing OA Framework Repository for Header Status...\n');
  
  const files = getAllFiles();
  console.log(`Found ${files.length} files to analyze\n`);
  
  const analysis = {
    total: files.length,
    byHeaderStatus: {},
    byTier: {},
    byCategory: {},
    byPriority: {},
    largeFiles: [],
    needsUpgrade: []
  };
  
  files.forEach(filePath => {
    const headerStatus = analyzeHeaderFormat(filePath);
    const stats = getFileStats(filePath);
    const { tier, category, priority } = categorizeFile(filePath);
    
    // Count by header status
    analysis.byHeaderStatus[headerStatus] = (analysis.byHeaderStatus[headerStatus] || 0) + 1;
    analysis.byTier[tier] = (analysis.byTier[tier] || 0) + 1;
    analysis.byCategory[category] = (analysis.byCategory[category] || 0) + 1;
    analysis.byPriority[priority] = (analysis.byPriority[priority] || 0) + 1;
    
    // Track large files (>700 lines need AI context sections)
    if (stats.lines > 700) {
      analysis.largeFiles.push({
        path: filePath,
        lines: stats.lines,
        headerStatus,
        priority
      });
    }
    
    // Track files needing upgrade
    if (headerStatus !== 'v2.3') {
      analysis.needsUpgrade.push({
        path: filePath,
        headerStatus,
        lines: stats.lines,
        size: stats.size,
        tier,
        category,
        priority
      });
    }
  });
  
  return analysis;
}

// Generate report
const analysis = analyzeRepository();

console.log('📊 REPOSITORY ANALYSIS RESULTS');
console.log('================================\n');

console.log(`📁 Total Files: ${analysis.total}`);
console.log(`🔄 Files Needing Upgrade: ${analysis.needsUpgrade.length} (${Math.round(analysis.needsUpgrade.length / analysis.total * 100)}%)`);
console.log(`✅ Files with v2.3 Headers: ${analysis.byHeaderStatus['v2.3'] || 0} (${Math.round((analysis.byHeaderStatus['v2.3'] || 0) / analysis.total * 100)}%)\n`);

console.log('📋 BY HEADER STATUS:');
Object.entries(analysis.byHeaderStatus).forEach(([status, count]) => {
  console.log(`  ${status}: ${count} files`);
});

console.log('\n🏗️ BY TIER:');
Object.entries(analysis.byTier).forEach(([tier, count]) => {
  console.log(`  ${tier}: ${count} files`);
});

console.log('\n📝 BY CATEGORY:');
Object.entries(analysis.byCategory).forEach(([category, count]) => {
  console.log(`  ${category}: ${count} files`);
});

console.log('\n⚡ BY PRIORITY:');
Object.entries(analysis.byPriority).forEach(([priority, count]) => {
  console.log(`  ${priority}: ${count} files`);
});

console.log(`\n📏 LARGE FILES (>700 lines): ${analysis.largeFiles.length}`);
analysis.largeFiles.slice(0, 10).forEach(file => {
  console.log(`  ${file.path} (${file.lines} lines, ${file.headerStatus}, ${file.priority})`);
});

// Save detailed results
fs.writeFileSync('header-analysis-results.json', JSON.stringify(analysis, null, 2));
console.log('\n💾 Detailed results saved to header-analysis-results.json');
