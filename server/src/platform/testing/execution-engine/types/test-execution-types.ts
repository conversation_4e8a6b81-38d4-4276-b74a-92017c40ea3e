/**
 * ============================================================================
 * AI CONTEXT: Test Execution Engine Types - Type Definitions for M0 Test Engine
 * Purpose: Comprehensive type definitions for test execution and component validation
 * Complexity: Moderate - Type definitions with enterprise validation requirements
 * AI Navigation: 4 sections, type definition domain
 * Lines: Target ≤300 LOC (Type definitions file)
 * ============================================================================
 */

/**
 * Test Execution Engine Types for OA Framework
 *
 * Comprehensive type definitions supporting the M0 Component Test Execution Engine
 * with enterprise-grade validation, performance monitoring, and resilient timing
 * integration requirements.
 *
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.3)
 * @authority-level type-definition-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-M0.1-001-enterprise-enhancement-architecture
 * @governance-status approved
 * @governance-compliance authority-validated
 */

// ============================================================================
// SECTION 1: ENGINE RESULT TYPES
// AI Context: Result types for test engine operations
// ============================================================================

/**
 * Test engine initialization result
 */
export interface TTestEngineInitResult {
  success: boolean;
  engineId: string;
  configuration: TTestExecutionEngineConfig;
  timestamp: Date;
  metadata: {
    orchestrationDriverIntegration: boolean;
    m0FoundationValidation: boolean;
    performanceThresholds: {
      responseTime: number;
      memoryUsage: number;
      cpuUsage: number;
    };
  };
}

/**
 * Test execution start result
 */
export interface TTestExecutionStartResult {
  success: boolean;
  executionId: string;
  startTime: Date;
  configuration: TTestExecutionEngineConfig;
  metadata: {
    engineVersion: string;
    maxConcurrentTests: number;
    performanceThresholds: {
      responseTime: number;
      memoryUsage: number;
      cpuUsage: number;
    };
  };
}

/**
 * Test execution stop result
 */
export interface TTestExecutionStopResult {
  success: boolean;
  stopTime: Date;
  stoppedTests: number;
  metadata: {
    totalTestsExecuted: number;
    activeTestsStopped: number;
  };
}

// ============================================================================
// SECTION 2: VALIDATION TYPES
// AI Context: Component validation and rule types
// ============================================================================

/**
 * Validation rule definition
 */
export interface TValidationRule {
  ruleId: string;
  ruleName: string;
  ruleType: 'performance' | 'security' | 'compliance' | 'functional';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  criteria: Record<string, any>;
  enabled: boolean;
}

/**
 * Component validation result
 */
export interface TComponentValidationResult {
  componentId: string;
  isValid: boolean;
  validationScore: number;
  validatedAt: Date;
  validationRules: TValidationRule[];
  findings: TValidationFinding[];
  metadata: {
    engineVersion: string;
    validationType: string;
    [key: string]: any;
  };
}

/**
 * Validation finding
 */
export interface TValidationFinding {
  findingId: string;
  ruleId: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  location?: string;
  suggestion?: string;
  metadata?: Record<string, any>;
}

/**
 * Integration validation result
 */
export interface TIntegrationValidationResult {
  integrationId: string;
  componentIds: string[];
  isValid: boolean;
  validationScore: number;
  validatedAt: Date;
  integrationFindings: TValidationFinding[];
  componentResults: TComponentValidationResult[];
  metadata: {
    engineVersion: string;
    integrationType: string;
  };
}

/**
 * Dependency validation result
 */
export interface TDependencyValidationResult {
  componentId: string;
  dependencies: string[];
  isValid: boolean;
  validationScore: number;
  validatedAt: Date;
  dependencyFindings: TValidationFinding[];
  metadata: {
    engineVersion: string;
    dependencyType: string;
  };
}

/**
 * Performance validation result
 */
export interface TPerformanceValidationResult {
  componentId: string;
  isValid: boolean;
  performanceScore: number;
  validatedAt: Date;
  performanceMetrics: {
    responseTime: number;
    memoryUsage: number;
    cpuUsage: number;
    throughput: number;
  };
  thresholds: {
    responseTime: number;
    memoryUsage: number;
    cpuUsage: number;
    throughput: number;
  };
  findings: TValidationFinding[];
  metadata: {
    engineVersion: string;
    performanceType: string;
  };
}

/**
 * Batch validation result
 */
export interface TBatchValidationResult {
  batchId: string;
  componentIds: string[];
  isValid: boolean;
  validationScore: number;
  validatedAt: Date;
  componentResults: TComponentValidationResult[];
  batchFindings: TValidationFinding[];
  metadata: {
    engineVersion: string;
    batchType: string;
    totalComponents: number;
    validComponents: number;
    invalidComponents: number;
  };
}

/**
 * Foundation validation result
 */
export interface TFoundationValidationResult {
  foundationId: string;
  isValid: boolean;
  validationScore: number;
  validatedAt: Date;
  componentResults: TComponentValidationResult[];
  foundationFindings: TValidationFinding[];
  metadata: {
    engineVersion: string;
    foundationType: string;
    totalComponents: number;
    validComponents: number;
    criticalIssues: number;
  };
}

// ============================================================================
// SECTION 3: PERFORMANCE & MONITORING TYPES
// AI Context: Performance metrics and monitoring types
// ============================================================================

/**
 * Test performance metrics
 */
export interface TTestPerformanceMetrics {
  testId: string;
  executionTime: number;
  memoryUsage: number;
  cpuUsage: number;
  throughput: number;
  errorRate: number;
  timestamp: Date;
  metadata: {
    engineVersion: string;
    testType: string;
    environment: string;
  };
}

/**
 * Test health status
 */
export interface TTestHealthStatus {
  engineId: string;
  status: 'healthy' | 'degraded' | 'unhealthy';
  healthScore: number;
  lastChecked: Date;
  healthMetrics: {
    activeTests: number;
    completedTests: number;
    failedTests: number;
    averageExecutionTime: number;
    errorRate: number;
  };
  issues: THealthIssue[];
  metadata: {
    engineVersion: string;
    environment: string;
  };
}

/**
 * Health issue
 */
export interface THealthIssue {
  issueId: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  component: string;
  detectedAt: Date;
  suggestion?: string;
  metadata?: Record<string, any>;
}

/**
 * Validation metrics
 */
export interface TValidationMetrics {
  validationId: string;
  totalValidations: number;
  successfulValidations: number;
  failedValidations: number;
  averageValidationTime: number;
  validationScore: number;
  timestamp: Date;
  metadata: {
    engineVersion: string;
    validationType: string;
    environment: string;
  };
}

// ============================================================================
// SECTION 4: CONFIGURATION TYPES
// AI Context: Configuration and engine setup types
// ============================================================================

/**
 * Test execution engine configuration (re-export for consistency)
 */
export interface TTestExecutionEngineConfig {
  // Engine Settings
  maxConcurrentTests: number;
  testTimeout: number;
  retryAttempts: number;
  
  // Performance Settings
  performanceThresholds: {
    responseTime: number;
    memoryUsage: number;
    cpuUsage: number;
  };
  
  // Validation Settings
  validationRules: TValidationRule[];
  strictMode: boolean;
  
  // Monitoring Settings
  metricsEnabled: boolean;
  reportingEnabled: boolean;
  
  // Integration Settings
  orchestrationDriverIntegration: boolean;
  m0FoundationValidation: boolean;
}

// Types are already exported above, no need for re-export
