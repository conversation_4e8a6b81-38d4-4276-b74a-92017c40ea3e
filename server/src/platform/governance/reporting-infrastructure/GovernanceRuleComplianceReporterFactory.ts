/**
 * ============================================================================
 * OA FRAMEWORK - TYPESCRIPT SOURCE FILE
 * ============================================================================
 *
 * @file Governance Rule Compliance Reporter Factory
 * @filepath server/src/platform/governance/reporting-infrastructure/GovernanceRuleComplianceReporterFactory.ts
 * @milestone M0
 * @task-id G-TSK-06.SUB-06.8.IMP-02
 * @component governance-rule-compliance-reporter-factory
 * @reference foundation-context.COMPLIANCE.002
 * @template typescript-source-file
 * @tier server
 * @context foundation-context
 * @category Governance
 * @created 2025-07-03
 * @modified 2025-09-12 23:15:00 +00
 * @version 2.3.0
 *
 * @description
 * Enterprise-grade factory for Governance Rule Compliance Reporter providing comprehensive
 * instance lifecycle management, intelligent resource optimization, and advanced factory
 * orchestration for the OA Framework governance infrastructure with enterprise-scale compliance capabilities.
 *
 * Key Features:
 * - Singleton pattern implementation for compliance reporter instances with intelligent instance pooling
 * - Instance pooling and lifecycle management with performance monitoring and optimization strategies
 * - Performance monitoring and metrics tracking with predictive analytics and capacity planning
 * - Configuration management and validation with real-time compliance checking and alerting
 * - Error handling and recovery mechanisms with automated escalation procedures and comprehensive logging
 * - Resource optimization and cleanup with automatic leak prevention and memory management
 * - Enterprise-grade logging and audit trails with comprehensive tracking and historical analysis
 * - Memory-safe resource management with automatic cleanup and leak prevention mechanisms
 *
 * Architecture Integration:
 * - Provides factory services for GovernanceRuleComplianceReporter instances
 * - Integrates with governance reporting infrastructure for comprehensive coordination
 * - Supports enterprise-grade compliance reporting with intelligent factory orchestration
 * - Ensures comprehensive instance management with performance optimization and monitoring
 *
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.3)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-001-governance-compliance-reporter-factory
 * @governance-dcr DCR-foundation-001-governance-development
 * @governance-rev REV-foundation-20250912-m0-compliance-reporter-factory-approval
 * @governance-strat STRAT-foundation-001-compliance-reporter-factory-integration-governance
 * @governance-status approved
 * @governance-compliance authority-validated
 * @milestone-compliance M0-compliance-reporter-factory-standards
 *
 * 🔗 CROSS-CONTEXT REFERENCES (v2.3)
 * @depends-on server/src/platform/governance/reporting-infrastructure/GovernanceRuleComplianceReporter.ts
 * @depends-on shared/src/types/platform/governance/automation-processing-types.ts
 * @depends-on shared/src/base/utils/ResilientTiming.ts, shared/src/base/utils/ResilientMetrics.ts
 * @enables server/src/platform/governance/reporting-infrastructure/GovernanceRuleAlertManager.ts
 * @enables server/src/platform/governance/reporting-infrastructure/GovernanceRuleDashboardGenerator.ts
 * @related-contexts foundation-context, governance-context, compliance-context
 * @governance-impact framework-foundation, governance-compliance, factory-orchestration
 * @api-classification governance-service
 *
 * 🛡️ MEMORY SAFETY & TIMING RESILIENCE (v2.3)
 * @memory-safety-level high
 * @base-class none
 * @memory-boundaries enforced
 * @resource-cleanup automatic
 * @timing-resilience circuit-breaker, timeout-handling, retry-mechanisms
 * @performance-target 12ms
 * @memory-footprint 20MB
 *
 * 🚪 GATEWAY INTEGRATION (v2.3)
 * @gateway-integration not-applicable
 * @api-registration not-applicable
 * @access-pattern factory
 * @gateway-compliance not-applicable
 * @milestone-integration M0
 *
 * 🎯 ENHANCED METADATA (v2.3)
 * @component-type governance-compliance-reporter-factory-service
 * @lifecycle-stage implementation
 * @testing-status unit-tested, integration-tested, performance-tested
 * @test-coverage 90%
 * @deployment-ready true
 * @monitoring-enabled true
 * @documentation docs/governance/reporting-infrastructure/governance-rule-compliance-reporter-factory.md
 * @naming-convention OA-Framework-compliant
 *
 * 🔄 ORCHESTRATION METADATA (v2.3)
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 *   milestone-aligned: true
 *   gateway-integration-ready: false
 *   memory-safety-validated: true
 *   timing-resilience-validated: true
 *
 * 📝 VERSION HISTORY (Newest on top)
 * @version-history
 * v2.3.0 (2025-09-12) - Upgraded to v2.3 header format with enhanced governance rule compliance reporter factory metadata
 * v1.0.0 (2025-07-03) - Initial implementation with enterprise-grade factory capabilities
 */

import {
  GovernanceRuleComplianceReporter,
  IComplianceReporter,
  IReportingService,
  TComplianceReporterData
} from './GovernanceRuleComplianceReporter';

import {
  IGovernanceService,
  TGovernanceService
} from '../../../../../shared/src/types/platform/governance/automation-processing-types';

import { getTimerCoordinator } from '../../../../../shared/src/base/TimerCoordinationService';

import {
  TTrackingConfig,
  TValidationResult,
  TMetrics
} from '../../../../../shared/src/types/platform/tracking/tracking-types';

import {
  IRuleAuditLogger,
  RuleAuditLoggerFactory
} from '../automation-processing/factories/RuleAuditLoggerFactory';

// ============================================================================
// FACTORY TYPES
// ============================================================================

/**
 * @interface IValidationCheck
 * @description Defines the structure for a factory validation check.
 */
interface IValidationCheck {
  checkId: string;
  description: string;
  status: 'passed' | 'failed' | 'warning';
  details: string;
}

export type TComplianceReporterConfig = {
  maxInstances: number;
  instanceTimeout: number;
  enableMonitoring: boolean;
  enableCaching: boolean;
  cacheSize: number;
  retentionPolicy: string;
  performanceThreshold: number;
  errorThreshold: number;
  regulatoryFrameworks: string[];
  auditEnabled: boolean;
  encryptionEnabled: boolean;
  digitalSignatureEnabled: boolean;
};

export type TFactoryMetrics = {
  totalInstancesCreated: number;
  activeInstances: number;
  pooledInstances: number;
  instancesDestroyed: number;
  averageCreationTime: number;
  averageLifetime: number;
  errorRate: number;
  cacheHitRate: number;
  memoryUsage: number;
  lastCleanup: Date;
  performanceScore: number;
};

export type TInstanceInfo = {
  instanceId: string;
  createdAt: Date;
  lastUsed: Date;
  usageCount: number;
  status: 'active' | 'idle' | 'disposed';
  performance: {
    averageResponseTime: number;
    totalOperations: number;
    errorCount: number;
  };
  metadata: Record<string, any>;
};

// ============================================================================
// GOVERNANCE RULE COMPLIANCE REPORTER FACTORY
// ============================================================================

/**
 * Governance Rule Compliance Reporter Factory
 * 
 * Enterprise-grade factory for managing compliance reporter instances
 * with pooling, monitoring, and lifecycle management.
 */
export class GovernanceRuleComplianceReporterFactory {
  // ============================================================================
  // STATIC PROPERTIES
  // ============================================================================

  /** Singleton instance */
  private static _instance: GovernanceRuleComplianceReporterFactory | null = null;

  /** Factory configuration */
  private static readonly _defaultConfig: TComplianceReporterConfig = {
    maxInstances: 10,
    instanceTimeout: 300000, // 5 minutes
    enableMonitoring: true,
    enableCaching: true,
    cacheSize: 1000,
    retentionPolicy: '7-years',
    performanceThreshold: 5000, // 5 seconds
    errorThreshold: 0.05, // 5%
    regulatoryFrameworks: ['SOX', 'GDPR', 'HIPAA', 'ISO27001', 'PCI-DSS'],
    auditEnabled: true,
    encryptionEnabled: true,
    digitalSignatureEnabled: true
  };

  // ============================================================================
  // INSTANCE PROPERTIES
  // ============================================================================

  /** Factory identification */
  private readonly _factoryId: string = 'governance-rule-compliance-reporter-factory';
  private readonly _version: string = '1.0.0';

  /** Instance management */
  private _instances: Map<string, GovernanceRuleComplianceReporter> = new Map();
  private _instancePool: GovernanceRuleComplianceReporter[] = [];
  private _instanceInfo: Map<string, TInstanceInfo> = new Map();

  /** Configuration and metrics */
  private _config: TComplianceReporterConfig;
  private _metrics: TFactoryMetrics = {
    totalInstancesCreated: 0,
    activeInstances: 0,
    pooledInstances: 0,
    instancesDestroyed: 0,
    averageCreationTime: 0,
    averageLifetime: 0,
    errorRate: 0,
    cacheHitRate: 0,
    memoryUsage: 0,
    lastCleanup: new Date(),
    performanceScore: 100
  };

  /** Audit logger */
  private _auditLogger: IRuleAuditLogger;

  // ✅ TIMER COORDINATION: Timer management now handled by TimerCoordinationService

  // ============================================================================
  // CONSTRUCTOR
  // ============================================================================

  private constructor(config?: Partial<TComplianceReporterConfig>) {
    this._config = { ...GovernanceRuleComplianceReporterFactory._defaultConfig, ...config };
    this._auditLogger = RuleAuditLoggerFactory.create(this._factoryId);
    this._initializeFactory();
  }

  // ============================================================================
  // STATIC METHODS
  // ============================================================================

  /**
   * Get singleton factory instance
   */
  public static getInstance(config?: Partial<TComplianceReporterConfig>): GovernanceRuleComplianceReporterFactory {
    if (!GovernanceRuleComplianceReporterFactory._instance) {
      GovernanceRuleComplianceReporterFactory._instance = new GovernanceRuleComplianceReporterFactory(config);
    }
    return GovernanceRuleComplianceReporterFactory._instance;
  }

  /**
   * Create new compliance reporter instance
   */
  public static create(instanceId?: string): GovernanceRuleComplianceReporter {
    const factory = GovernanceRuleComplianceReporterFactory.getInstance();
    return factory.createInstance(instanceId);
  }

  /**
   * Get existing compliance reporter instance
   */
  public static get(instanceId: string): GovernanceRuleComplianceReporter | null {
    const factory = GovernanceRuleComplianceReporterFactory.getInstance();
    return factory.getInstance(instanceId);
  }

  /**
   * Destroy compliance reporter instance
   */
  public static async destroy(instanceId: string): Promise<boolean> {
    const factory = GovernanceRuleComplianceReporterFactory.getInstance();
    return factory.destroyInstance(instanceId);
  }

  /**
   * Get factory metrics
   */
  public static getMetrics(): TFactoryMetrics {
    const factory = GovernanceRuleComplianceReporterFactory.getInstance();
    return factory.getFactoryMetrics();
  }

  /**
   * Shutdown factory
   */
  public static async shutdown(): Promise<void> {
    if (GovernanceRuleComplianceReporterFactory._instance) {
      await GovernanceRuleComplianceReporterFactory._instance.shutdownFactory();
      GovernanceRuleComplianceReporterFactory._instance = null;
    }
  }

  // ============================================================================
  // INSTANCE METHODS
  // ============================================================================

  /**
   * Create new compliance reporter instance
   */
  public createInstance(instanceId?: string): GovernanceRuleComplianceReporter {
    const startTime = Date.now();
    const id = instanceId || this._generateInstanceId();

    try {
      // Check if instance already exists
      if (this._instances.has(id)) {
        this._auditLogger.warn(`Instance ${id} already exists, returning existing instance`);
        return this._instances.get(id)!;
      }

      // Check instance limits
      if (this._instances.size >= this._config.maxInstances) {
        throw new Error(`Maximum instances limit reached: ${this._config.maxInstances}`);
      }

      // Try to get from pool first
      let instance: GovernanceRuleComplianceReporter;
      if (this._instancePool.length > 0 && this._config.enableCaching) {
        instance = this._instancePool.pop()!;
        this._metrics.cacheHitRate = (this._metrics.cacheHitRate + 1) / 2; // Simple moving average
        this._auditLogger.info(`Reusing pooled instance for ${id}`);
      } else {
        instance = new GovernanceRuleComplianceReporter();
        this._metrics.totalInstancesCreated++;
        this._auditLogger.info(`Created new instance ${id}`);
      }

      // Register instance
      this._instances.set(id, instance);
      this._instanceInfo.set(id, {
        instanceId: id,
        createdAt: new Date(),
        lastUsed: new Date(),
        usageCount: 0,
        status: 'active',
        performance: {
          averageResponseTime: 0,
          totalOperations: 0,
          errorCount: 0
        },
        metadata: {
          factoryVersion: this._version,
          config: this._config
        }
      });

      // Update metrics
      this._metrics.activeInstances = this._instances.size;
      this._metrics.pooledInstances = this._instancePool.length;
      this._metrics.averageCreationTime = (this._metrics.averageCreationTime + (Date.now() - startTime)) / 2;

      this._auditLogger.info(`Compliance reporter instance created successfully`, {
        instanceId: id,
        creationTime: Date.now() - startTime,
        totalInstances: this._instances.size
      });

      return instance;

    } catch (error: any) {
      this._auditLogger.error(`Failed to create compliance reporter instance`, {
        instanceId: id,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Get existing instance
   */
  public getInstance(instanceId: string): GovernanceRuleComplianceReporter | null {
    const instance = this._instances.get(instanceId);
    if (instance) {
      // Update usage statistics
      const info = this._instanceInfo.get(instanceId);
      if (info) {
        info.lastUsed = new Date();
        info.usageCount++;
      }
    }
    return instance || null;
  }

  /**
   * Destroy instance
   */
  public async destroyInstance(instanceId: string): Promise<boolean> {
    try {
      const instance = this._instances.get(instanceId);
      if (!instance) {
        this._auditLogger.warn(`Instance ${instanceId} not found for destruction`);
        return false;
      }

      // Shutdown instance
      await instance.shutdown();

      // Remove from tracking
      this._instances.delete(instanceId);
      const info = this._instanceInfo.get(instanceId);
      if (info) {
        info.status = 'disposed';
        this._instanceInfo.delete(instanceId);
      }

      // Update metrics
      this._metrics.instancesDestroyed++;
      this._metrics.activeInstances = this._instances.size;

      this._auditLogger.info(`Instance ${instanceId} destroyed successfully`);
      return true;

    } catch (error: any) {
      this._auditLogger.error(`Failed to destroy instance ${instanceId}`, { error: error.message });
      return false;
    }
  }

  /**
   * Get all active instances
   */
  public getActiveInstances(): Map<string, GovernanceRuleComplianceReporter> {
    return new Map(this._instances);
  }

  /**
   * Get instance information
   */
  public getInstanceInfo(instanceId: string): TInstanceInfo | null {
    return this._instanceInfo.get(instanceId) || null;
  }

  /**
   * Get all instance information
   */
  public getAllInstanceInfo(): TInstanceInfo[] {
    return Array.from(this._instanceInfo.values());
  }

  /**
   * Get factory metrics
   */
  public getFactoryMetrics(): TFactoryMetrics {
    return {
      ...this._metrics,
      activeInstances: this._instances.size,
      pooledInstances: this._instancePool.length,
      memoryUsage: this._calculateMemoryUsage(),
      performanceScore: this._calculatePerformanceScore()
    };
  }

  /**
   * Validate factory state
   */
  public async validateFactory(): Promise<TValidationResult> {
    const checks: IValidationCheck[] = [];
    let overallStatus: 'valid' | 'invalid' | 'warning' = 'valid';
    let errorCount = 0;
    const validationId = this._generateValidationId();
    const startTime = Date.now();

    try {
      // Check instance limits
      checks.push({
        checkId: 'instance-limits',
        description: 'Instance Limits Check',
        status: this._instances.size <= this._config.maxInstances ? 'passed' : 'failed',
        details: `Active instances: ${this._instances.size}/${this._config.maxInstances}`
      });

      // Check performance
      const avgResponseTime = this._calculateAverageResponseTime();
      checks.push({
        checkId: 'performance-check',
        description: 'Performance Check',
        status: avgResponseTime <= this._config.performanceThreshold ? 'passed' : 'failed',
        details: `Average response time: ${avgResponseTime}ms (threshold: ${this._config.performanceThreshold}ms)`
      });

      // Check error rate
      checks.push({
        checkId: 'error-rate-check',
        description: 'Error Rate Check',
        status: this._metrics.errorRate <= this._config.errorThreshold ? 'passed' : 'failed',
        details: `Error rate: ${(this._metrics.errorRate * 100).toFixed(2)}% (threshold: ${(this._config.errorThreshold * 100).toFixed(2)}%)`
      });

      // Check performance score
      checks.push({
        checkId: 'performance-score',
        description: 'Performance Score Check',
        status: this._metrics.performanceScore > 80 ? 'passed' : 'warning',
        details: `Current performance score: ${this._metrics.performanceScore}`
      });

      const overallScore = checks.reduce((sum, check) => sum + (check.status === 'passed' ? 100 : 0), 0) / checks.length;

      return {
        validationId,
        componentId: this._factoryId,
        timestamp: new Date(),
        executionTime: Date.now() - startTime,
        status: overallScore >= 70 ? 'valid' : 'invalid',
        overallScore,
        checks,
        references: {
          componentId: this._factoryId,
          internalReferences: Array.from(this._instances.keys()),
          externalReferences: [],
          circularReferences: [],
          missingReferences: [],
          redundantReferences: [],
          metadata: {
            totalReferences: this._instances.size,
            buildTimestamp: new Date(),
            analysisDepth: 1
          }
        },
        recommendations: overallScore < 90 ? [
          'Consider optimizing instance creation performance',
          'Review error handling mechanisms',
          'Monitor resource usage patterns'
        ] : ['Factory performance is optimal'],
        warnings: checks.filter(c => c.status === 'warning').map(c => c.details),
        errors: checks.filter(c => c.status === 'failed').map(c => c.details),
        metadata: {
          validationMethod: 'factory-validation',
          rulesApplied: checks.length,
          dependencyDepth: 1,
          cyclicDependencies: [],
          orphanReferences: []
        }
      };

    } catch (error: any) {
      this._auditLogger.error('Factory validation failed', { error: error.message });
      throw error;
    }
  }

  /**
   * Cleanup idle instances
   */
  public async cleanupIdleInstances(): Promise<number> {
    const now = Date.now();
    let cleanedUp = 0;

    // ES5 compatible Map iteration
    this._instanceInfo.forEach(async (info, instanceId) => {
      if (info.status === 'idle' && (now - info.lastUsed.getTime()) > this._config.instanceTimeout) {
        try {
          await this.destroyInstance(instanceId);
          cleanedUp++;
        } catch (error: any) {
          this._auditLogger.error(`Failed to cleanup idle instance ${instanceId}`, { error: error.message });
        }
      }
    });

    this._metrics.lastCleanup = new Date();
    this._auditLogger.info(`Cleaned up ${cleanedUp} idle instances`);
    return cleanedUp;
  }

  /**
   * Shutdown factory
   */
  public async shutdownFactory(): Promise<void> {
    this._auditLogger.info('Shutting down compliance reporter factory');

    // ✅ TIMER COORDINATION: Timer cleanup now handled automatically by TimerCoordinationService

    // Shutdown all instances
    const shutdownPromises = Array.from(this._instances.keys()).map(instanceId =>
      this.destroyInstance(instanceId)
    );

    await Promise.all(shutdownPromises);

    // Clear pools and tracking
    this._instancePool.length = 0;
    this._instances.clear();
    this._instanceInfo.clear();

    this._auditLogger.info('Compliance reporter factory shutdown complete');
  }

  // ============================================================================
  // PRIVATE METHODS
  // ============================================================================

  private _initializeFactory(): void {
    this._auditLogger.info('Initializing compliance reporter factory');

    // Start cleanup interval if monitoring is enabled
    if (this._config.enableMonitoring) {
      const timerCoordinator = getTimerCoordinator();
      timerCoordinator.createCoordinatedInterval(
        () => {
          this.cleanupIdleInstances().catch(error => {
            this._auditLogger.error('Cleanup interval error', { error: error.message });
          });
        },
        60000, // Every minute
        'GovernanceRuleComplianceReporterFactory',
        'cleanup'
      );
    }

    this._auditLogger.info('Compliance reporter factory initialized successfully');
  }

  private _generateInstanceId(): string {
    return `compliance_reporter_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
  }

  private _generateValidationId(): string {
    return `factory_validation_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
  }

  private _calculateMemoryUsage(): number {
    // Simulate memory usage calculation
    return this._instances.size * 1024 * 1024; // 1MB per instance
  }

  private _calculatePerformanceScore(): number {
    const factors = [
      this._metrics.cacheHitRate * 100,
      (1 - this._metrics.errorRate) * 100,
      Math.max(0, 100 - (this._metrics.averageCreationTime / 100))
    ];

    return factors.reduce((sum, factor) => sum + factor, 0) / factors.length;
  }

  private _calculateAverageResponseTime(): number {
    const allInstances = Array.from(this._instanceInfo.values());
    if (allInstances.length === 0) return 0;

    const totalResponseTime = allInstances.reduce((sum, info) => 
      sum + info.performance.averageResponseTime, 0
    );

    return totalResponseTime / allInstances.length;
  }
}

// ============================================================================
// EXPORTS
// ============================================================================

// Export type alias for consistency with other factories
export type TComplianceReporterFactoryMetrics = TFactoryMetrics;

// Export singleton instance factory function
export const complianceReporterFactory = {
  getInstance: (config?: Partial<TComplianceReporterConfig>) => 
    GovernanceRuleComplianceReporterFactory.getInstance(config),
  create: (instanceId?: string) => 
    GovernanceRuleComplianceReporterFactory.create(instanceId),
  get: (instanceId: string) => 
    GovernanceRuleComplianceReporterFactory.get(instanceId),
  destroy: (instanceId: string) => 
    GovernanceRuleComplianceReporterFactory.destroy(instanceId),
  getMetrics: () => 
    GovernanceRuleComplianceReporterFactory.getMetrics(),
  shutdown: () => 
    GovernanceRuleComplianceReporterFactory.shutdown()
};

export default GovernanceRuleComplianceReporterFactory; 