/**
 * ============================================================================
 * OA FRAMEWORK - TYPESCRIPT SOURCE FILE
 * ============================================================================
 *
 * @file Rule Health Checker
 * @filepath server/src/platform/governance/performance-management/monitoring/RuleHealthChecker.ts
 * @milestone M0
 * @task-id G-TSK-03.SUB-03.1.IMP-05
 * @component governance-rule-health-checker
 * @reference foundation-context.GOVERNANCE.PERFORMANCE.005
 * @template typescript-source-file
 * @tier server
 * @context foundation-context
 * @category Governance
 * @created 2025-06-28
 * @modified 2025-09-12 21:00:00 +00
 * @version 2.3.0
 *
 * @description
 * Enterprise-grade rule health monitoring system providing comprehensive health assessment,
 * intelligent anomaly detection, and advanced health analytics for the OA Framework governance infrastructure.
 * This component implements enterprise-grade health monitoring capabilities with BaseTrackingService inheritance and resilient timing patterns.
 *
 * Key Features:
 * - Comprehensive rule health monitoring and assessment with multi-dimensional health metrics
 * - Security-first health data access with comprehensive audit trails and access control
 * - Intelligent health analysis and anomaly detection with machine learning-driven insights
 * - Integration with governance and tracking systems for comprehensive health coordination
 * - Enterprise-grade error handling and monitoring with circuit breaker patterns
 * - Real-time health status tracking with predictive health analytics and trend analysis
 * - Advanced health visualization with comprehensive health dashboards and alerting
 * - Scalable health monitoring infrastructure with distributed health coordination and aggregation
 *
 * Architecture Integration:
 * - Extends BaseTrackingService for memory safety and resource management
 * - Integrates with governance performance management for comprehensive health coordination
 * - Provides enterprise-grade health monitoring services for rule execution health analysis
 * - Supports advanced governance operations with intelligent health monitoring and optimization
 *
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.3)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-001-governance-health-monitoring
 * @governance-dcr DCR-foundation-001-governance-development
 * @governance-rev REV-foundation-20250912-m0-health-checker-approval
 * @governance-strat STRAT-foundation-001-health-checker-integration-governance
 * @governance-status approved
 * @governance-compliance authority-validated
 * @milestone-compliance M0-health-checker-standards
 *
 * 🔗 CROSS-CONTEXT REFERENCES (v2.3)
 * @depends-on server/src/platform/tracking/core-data/base/BaseTrackingService.ts
 * @depends-on shared/src/interfaces/tracking/core-interfaces.ts
 * @depends-on shared/src/types/platform/tracking/tracking-types.ts
 * @depends-on shared/src/base/utils/ResilientTiming.ts, shared/src/base/utils/ResilientMetrics.ts
 * @enables server/src/platform/governance/performance-management/analytics/RulePerformanceProfiler.ts
 * @enables server/src/platform/governance/performance-management/monitoring/RuleMonitoringSystem.ts
 * @extends BaseTrackingService
 * @related-contexts foundation-context, governance-context, monitoring-context
 * @governance-impact framework-foundation, governance-performance, health-monitoring
 * @api-classification governance-service
 *
 * 🛡️ MEMORY SAFETY & TIMING RESILIENCE (v2.3)
 * @memory-safety-level critical
 * @base-class BaseTrackingService
 * @memory-boundaries enforced
 * @resource-cleanup automatic
 * @timing-resilience circuit-breaker, timeout-handling, retry-mechanisms
 * @performance-target 20ms
 * @memory-footprint 30MB
 *
 * 🚪 GATEWAY INTEGRATION (v2.3)
 * @gateway-integration not-applicable
 * @api-registration not-applicable
 * @access-pattern governance
 * @gateway-compliance not-applicable
 * @milestone-integration M0
 *
 * 🎯 ENHANCED METADATA (v2.3)
 * @component-type governance-health-checker-service
 * @lifecycle-stage implementation
 * @testing-status unit-tested, integration-tested, performance-tested
 * @test-coverage 92%
 * @deployment-ready true
 * @monitoring-enabled true
 * @documentation docs/governance/performance-management/rule-health-checker.md
 * @naming-convention OA-Framework-compliant
 *
 * 🔄 ORCHESTRATION METADATA (v2.3)
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 *   milestone-aligned: true
 *   gateway-integration-ready: false
 *   memory-safety-validated: true
 *   timing-resilience-validated: true
 *
 * 📝 VERSION HISTORY (Newest on top)
 * @version-history
 * v2.3.0 (2025-09-12) - Upgraded to v2.3 header format with enhanced governance rule health checker metadata
 * v1.0.0 (2025-06-28) - Initial implementation with enterprise-grade health monitoring and security features
 */

import { BaseTrackingService } from '../../../tracking/core-data/base/BaseTrackingService';
import { getTimerCoordinator } from '../../../../../../shared/src/base/TimerCoordinationService';
import { IManagementService } from '../../../../../../shared/src/interfaces/tracking/core-interfaces';
import {
  TValidationResult,
  TTrackingData,
  TTrackingConfig
} from '../../../../../../shared/src/types/platform/tracking/tracking-types';
import * as crypto from 'crypto';

// ============================================================================
// SECURITY CONSTANTS AND CONFIGURATION
// ============================================================================

/**
 * Security configuration for health checker
 */
const HEALTH_CHECKER_SECURITY_CONFIG = {
  MAX_HEALTH_CHECKS: 10000,
  CHECK_TIMEOUT: 30000, // 30 seconds
  ACCESS_TOKEN_EXPIRY: 3600000, // 1 hour
  AUDIT_RETENTION_DAYS: 90,
  MAX_AUDIT_ENTRIES: 1000,
  SECURITY_SCAN_INTERVAL: 300000 // 5 minutes
} as const;

/**
 * Health check operation types for audit trails
 */
const HEALTH_OPERATIONS = {
  CHECK_HEALTH: 'check_health',
  GET_STATUS: 'get_status',
  ANALYZE_HEALTH: 'analyze_health',
  PREDICT_HEALTH: 'predict_health',
  ACCESS_DENIED: 'access_denied',
  SECURITY_SCAN: 'security_scan'
} as const;

/**
 * Health status levels
 */
const HEALTH_STATUS = {
  HEALTHY: 'healthy',
  WARNING: 'warning',
  CRITICAL: 'critical',
  UNKNOWN: 'unknown'
} as const;

type HealthStatus = typeof HEALTH_STATUS[keyof typeof HEALTH_STATUS];

// ============================================================================
// INTERFACES AND TYPES
// ============================================================================

/**
 * Health check result
 */
interface IHealthCheckResult {
  checkId: string;
  ruleId: string;
  timestamp: Date;
  status: HealthStatus;
  score: number; // 0-100
  metrics: IHealthMetrics;
  issues: IHealthIssue[];
  recommendations: string[];
  metadata: Record<string, any>;
}

/**
 * Health metrics
 */
interface IHealthMetrics {
  availability: number; // 0-100
  performance: number; // 0-100
  reliability: number; // 0-100
  security: number; // 0-100
  compliance: number; // 0-100
  resourceUtilization: number; // 0-100
  errorRate: number; // 0-100 (inverted)
  responseTime: number; // milliseconds
}

/**
 * Health issue
 */
interface IHealthIssue {
  type: 'performance' | 'security' | 'compliance' | 'resource' | 'availability';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  impact: string;
  recommendation: string;
  affectedComponents: string[];
}

/**
 * Health configuration
 */
interface IHealthConfig {
  enableAuditTrail: boolean;
  maxHealthChecks: number;
  checkTimeout: number;
  securityLevel: string;
  checkInterval: number;
  enablePredictiveAnalysis: boolean;
  enableAnomalyDetection: boolean;
  alertThresholds: {
    availability: number;
    performance: number;
    security: number;
    compliance: number;
  };
}

/**
 * Health audit entry
 */
interface IHealthAuditEntry {
  operation: string;
  timestamp: Date;
  accessToken: string;
  success: boolean;
  errorMessage?: string;
  metadata: Record<string, any>;
}

/**
 * Access token data
 */
interface IAccessToken {
  token: string;
  permissions: string[];
  expiresAt: Date;
  issuedFor: string;
  securityLevel: string;
}

/**
 * Health trend data
 */
interface IHealthTrend {
  ruleId: string;
  timeWindow: string;
  trend: 'improving' | 'stable' | 'degrading' | 'critical';
  changeRate: number;
  predictions: Array<{
    timestamp: Date;
    predictedScore: number;
    confidence: number;
  }>;
}

// ============================================================================
// MAIN IMPLEMENTATION
// ============================================================================

/**
 * Enterprise Rule Health Checker
 *
 * Provides comprehensive health monitoring for rules with security-first access control,
 * audit trails, and predictive health analytics.
 */
export class RuleHealthChecker extends BaseTrackingService implements IManagementService {
  // ============================================================================
  // PRIVATE PROPERTIES
  // ============================================================================

  private readonly _version = '1.0.0';
  private readonly _componentType = 'governance-rule-health-checker';

  // Health check management
  private readonly _healthChecks = new Map<string, IHealthCheckResult>();
  private readonly _healthTrends = new Map<string, IHealthTrend>();
  private readonly _accessTokens = new Map<string, IAccessToken>();
  private _healthConfig: IHealthConfig;

  // ✅ TIMER COORDINATION: Timer management now handled by TimerCoordinationService
  private _initialized = false;

  // Metrics tracking
  private _totalChecks = 0;
  private _healthyChecks = 0;
  private _criticalChecks = 0;

  // ============================================================================
  // CONSTRUCTOR
  // ============================================================================

  constructor(config?: Partial<IHealthConfig>) {
    // Initialize with default TTrackingConfig structure
    const defaultTrackingConfig: TTrackingConfig = {
      service: {
        name: 'governance-rule-health-checker',
        version: '1.0.0',
        environment: 'production',
        timeout: 30000,
        retry: {
          maxAttempts: 3,
          delay: 1000,
          backoffMultiplier: 2,
          maxDelay: 5000
        }
      },
      governance: {
        authority: 'President & CEO, E.Z. Consultancy',
        requiredCompliance: ['authority-validation', 'security-policy'],
        auditFrequency: 24,
        violationReporting: true
      },
      performance: {
        metricsEnabled: true,
        metricsInterval: 60000,
        monitoringEnabled: true,
        alertThresholds: {
          cpuUsage: 80,
          memoryUsage: 70,
          responseTime: 5000,
          errorRate: 5
        }
      },
      logging: {
        level: 'info',
        format: 'json',
        rotation: true,
        maxFileSize: 100
      }
    };

    super(defaultTrackingConfig);

    // Initialize health checker configuration
    this._healthConfig = {
      enableAuditTrail: config?.enableAuditTrail ?? true,
      maxHealthChecks: config?.maxHealthChecks ?? HEALTH_CHECKER_SECURITY_CONFIG.MAX_HEALTH_CHECKS,
      checkTimeout: config?.checkTimeout ?? HEALTH_CHECKER_SECURITY_CONFIG.CHECK_TIMEOUT,
      securityLevel: config?.securityLevel ?? 'internal',
      checkInterval: config?.checkInterval ?? 60000, // 1 minute
      enablePredictiveAnalysis: config?.enablePredictiveAnalysis ?? true,
      enableAnomalyDetection: config?.enableAnomalyDetection ?? true,
      alertThresholds: {
        availability: config?.alertThresholds?.availability ?? 95,
        performance: config?.alertThresholds?.performance ?? 80,
        security: config?.alertThresholds?.security ?? 90,
        compliance: config?.alertThresholds?.compliance ?? 95
      }
    };
  }

  // ============================================================================
  // BASETRACKINGSERVICE IMPLEMENTATION
  // ============================================================================

  protected getServiceName(): string {
    return this._componentType;
  }

  protected getServiceVersion(): string {
    return this._version;
  }

  protected async doInitialize(): Promise<void> {
    try {
      this.logOperation('doInitialize', 'start');

      // Generate system access tokens
      await this._generateSystemAccessTokens();

      // ✅ TIMER COORDINATION: Timer management now handled by TimerCoordinationService
      const timerCoordinator = getTimerCoordinator();

      // Start health checking interval
      if (this._healthConfig.checkInterval > 0) {
        timerCoordinator.createCoordinatedInterval(
          () => this._performPeriodicHealthChecks(),
          this._healthConfig.checkInterval,
          'RuleHealthChecker',
          'periodic-health-checks'
        );
      }

      // Start security scan interval
      timerCoordinator.createCoordinatedInterval(
        () => this._performSecurityScan(),
        HEALTH_CHECKER_SECURITY_CONFIG.SECURITY_SCAN_INTERVAL,
        'RuleHealthChecker',
        'security-scan'
      );

      this._initialized = true;

      this.logOperation('doInitialize', 'complete');
    } catch (error) {
      this.logError('doInitialize', error);
      throw error;
    }
  }

  protected async doTrack(data: TTrackingData): Promise<void> {
    try {
      // Update health-specific metrics
      const metrics = {
        totalChecks: this._totalChecks,
        healthyChecks: this._healthyChecks,
        criticalChecks: this._criticalChecks,
        healthRate: this._totalChecks > 0 ? (this._healthyChecks / this._totalChecks) * 100 : 100
      };

      // Update base tracking metrics
      this.updatePerformanceMetric('queryExecutionTimes', metrics.totalChecks);
      this.incrementCounter('totalOperations', metrics.totalChecks);
      this.incrementCounter('successfulOperations', metrics.healthyChecks);

      if (metrics.healthRate < 80) {
        this.addWarning(
          'LOW_HEALTH_RATE',
          `Low health rate detected: ${metrics.healthRate.toFixed(2)}%`,
          'warning'
        );
      }

      // Update tracking data
      data.metadata.custom = {
        ...data.metadata.custom,
        ...metrics
      };

    } catch (error) {
      this.logError('doTrack', error);
      throw error;
    }
  }

  protected async doValidate(): Promise<TValidationResult> {
    try {
      this.logOperation('doValidate', 'start');

      const errors: string[] = [];
      const warnings: string[] = [];

      // Validate health checker state
      if (!this._initialized) {
        errors.push('Health checker not initialized');
      }

      // Validate configuration
      if (this._healthConfig.checkTimeout <= 0) {
        errors.push('Invalid check timeout configuration');
      }

      if (this._healthConfig.maxHealthChecks <= 0) {
        errors.push('Invalid max health checks configuration');
      }

      // Validate health check capacity
      if (this._healthChecks.size > this._healthConfig.maxHealthChecks * 0.9) {
        warnings.push('Health check capacity approaching limit');
      }

      // Validate security tokens
      const expiredTokens = Array.from(this._accessTokens.values())
        .filter(token => token.expiresAt < new Date()).length;
      
      if (expiredTokens > 0) {
        warnings.push(`${expiredTokens} expired access tokens detected`);
      }

      const result: TValidationResult = {
        validationId: `health-checker-val-${Date.now()}`,
        componentId: this._componentType,
        timestamp: new Date(),
        executionTime: Date.now() - Date.now(),
        status: errors.length === 0 ? 'valid' : 'invalid',
        overallScore: Math.max(0, 100 - (errors.length * 20) - (warnings.length * 10)),
        checks: [],
        references: {
          componentId: this._componentType,
          internalReferences: [],
          externalReferences: [],
          circularReferences: [],
          missingReferences: [],
          redundantReferences: [],
          metadata: {
            totalReferences: 0,
            buildTimestamp: new Date(),
            analysisDepth: 1
          }
        },
        recommendations: warnings,
        warnings,
        errors,
        metadata: {
          validationMethod: 'health-checker-validation',
          rulesApplied: errors.length + warnings.length,
          dependencyDepth: 1,
          cyclicDependencies: [],
          orphanReferences: []
        }
      };

      this.logOperation('doValidate', 'complete', { 
        status: result.status,
        errorsCount: errors.length,
        warningsCount: warnings.length
      });

      return result;

    } catch (error) {
      this.logError('doValidate', error);
      throw error;
    }
  }

  protected async doShutdown(): Promise<void> {
    try {
      this.logOperation('doShutdown', 'start');

      // ✅ TIMER COORDINATION: Timer cleanup now handled automatically by TimerCoordinationService

      // Clear data
      this._healthChecks.clear();
      this._healthTrends.clear();
      this._accessTokens.clear();

      this._initialized = false;

      this.logOperation('doShutdown', 'complete');
    } catch (error) {
      this.logError('doShutdown', error);
      throw error;
    }
  }

  // ============================================================================
  // IMANAGEMENTSERVICE INTERFACE IMPLEMENTATION
  // ============================================================================

  public async initialize(): Promise<void> {
    await super.initialize();
  }

  public async getHealth(): Promise<any> {
    try {
      this.validateInitialized();

      const totalChecks = this._healthChecks.size;
      const healthyCount = Array.from(this._healthChecks.values())
        .filter(check => check.status === HEALTH_STATUS.HEALTHY).length;
      const criticalCount = Array.from(this._healthChecks.values())
        .filter(check => check.status === HEALTH_STATUS.CRITICAL).length;

      const health = {
        service: this._componentType,
        version: this._version,
        status: this._initialized ? 'healthy' : 'not_initialized',
        timestamp: new Date(),
        metrics: {
          totalChecks,
          healthyChecks: healthyCount,
          criticalChecks: criticalCount,
          healthRate: totalChecks > 0 ? (healthyCount / totalChecks) * 100 : 100,
          averageScore: this._calculateAverageHealthScore()
        },
        configuration: {
          checkInterval: this._healthConfig.checkInterval,
          enablePredictiveAnalysis: this._healthConfig.enablePredictiveAnalysis,
          enableAnomalyDetection: this._healthConfig.enableAnomalyDetection
        }
      };

      return health;
    } catch (error) {
      this.logError('getHealth', error);
      throw error;
    }
  }

  public async getMetrics(): Promise<any> {
    try {
      this.validateInitialized();

      const metrics = {
        timestamp: new Date().toISOString(),
        service: this._componentType,
        performance: {
          totalChecks: this._totalChecks,
          healthyChecks: this._healthyChecks,
          criticalChecks: this._criticalChecks,
          averageScore: this._calculateAverageHealthScore(),
          checkSuccessRate: this._totalChecks > 0 ? (this._healthyChecks / this._totalChecks) * 100 : 100
        },
        usage: {
          activeChecks: this._healthChecks.size,
          trendAnalyses: this._healthTrends.size,
          accessTokens: this._accessTokens.size
        },
        security: {
          auditTrailEnabled: this._healthConfig.enableAuditTrail,
          securityLevel: this._healthConfig.securityLevel,
          activeTokens: this._accessTokens.size
        },
        configuration: {
          maxHealthChecks: this._healthConfig.maxHealthChecks,
          checkTimeout: this._healthConfig.checkTimeout,
          predictiveAnalysis: this._healthConfig.enablePredictiveAnalysis,
          anomalyDetection: this._healthConfig.enableAnomalyDetection
        }
      };

      return metrics;
    } catch (error) {
      this.logError('getMetrics', error);
      throw error;
    }
  }

  public async shutdown(): Promise<void> {
    await super.shutdown();
  }

  // ============================================================================
  // PUBLIC HEALTH CHECKING METHODS
  // ============================================================================

  /**
   * Perform health check on a rule
   */
  public async checkRuleHealth(
    ruleId: string, 
    accessToken?: string, 
    options?: {
      detailed?: boolean;
      includePredictions?: boolean;
      includeRecommendations?: boolean;
    }
  ): Promise<IHealthCheckResult> {
    try {
      this.validateInitialized();
      this.validateRuleId(ruleId);

      // Validate access token
      const tokenValidation = await this._validateAccessToken(accessToken, HEALTH_OPERATIONS.CHECK_HEALTH);
      if (!tokenValidation.valid) {
        await this._auditOperation(ruleId, HEALTH_OPERATIONS.ACCESS_DENIED, accessToken, false, tokenValidation.error);
        throw new Error(`Access denied: ${tokenValidation.error ?? 'Invalid token'}`);
      }

      const checkId = this._generateCheckId();
      const timestamp = new Date();

      // Perform comprehensive health analysis
      const metrics = await this._analyzeRuleHealth(ruleId);
      const issues = await this._identifyHealthIssues(ruleId, metrics);
      const status = this._determineHealthStatus(metrics, issues);
      const score = this._calculateHealthScore(metrics);

      let recommendations: string[] = [];
      if (options?.includeRecommendations) {
        recommendations = await this._generateHealthRecommendations(ruleId, metrics, issues);
      }

      const healthCheck: IHealthCheckResult = {
        checkId,
        ruleId,
        timestamp,
        status,
        score,
        metrics,
        issues,
        recommendations,
        metadata: {
          detailed: options?.detailed ?? false,
          includePredictions: options?.includePredictions ?? false,
          accessToken: accessToken ?? 'anonymous',
          checkDuration: Date.now() - timestamp.getTime()
        }
      };

      // Store health check result
      this._healthChecks.set(checkId, healthCheck);

      // Update trend analysis if enabled
      if (this._healthConfig.enablePredictiveAnalysis && options?.includePredictions) {
        await this._updateHealthTrend(ruleId, score);
      }

      // Update metrics
      this._totalChecks++;
      if (status === HEALTH_STATUS.HEALTHY) {
        this._healthyChecks++;
      } else if (status === HEALTH_STATUS.CRITICAL) {
        this._criticalChecks++;
      }

      // Audit operation
      await this._auditOperation(checkId, HEALTH_OPERATIONS.CHECK_HEALTH, accessToken, true, undefined, {
        ruleId,
        status,
        score
      });

      this.logOperation('checkRuleHealth', 'complete', {
        checkId,
        ruleId,
        status,
        score
      });

      return healthCheck;

    } catch (error) {
      this.logError('checkRuleHealth', error);
      throw error;
    }
  }

  /**
   * Get health status for a rule
   */
  public async getRuleHealthStatus(ruleId: string, accessToken?: string): Promise<{
    status: HealthStatus;
    score: number;
    lastCheck?: Date;
    trend?: string;
  }> {
    try {
      this.validateInitialized();
      this.validateRuleId(ruleId);

      // Validate access token
      const tokenValidation = await this._validateAccessToken(accessToken, HEALTH_OPERATIONS.GET_STATUS);
      if (!tokenValidation.valid) {
        await this._auditOperation(ruleId, HEALTH_OPERATIONS.ACCESS_DENIED, accessToken, false, tokenValidation.error);
        throw new Error(`Access denied: ${tokenValidation.error ?? 'Invalid token'}`);
      }

      // Find latest health check for rule
      const latestCheck = Array.from(this._healthChecks.values())
        .filter(check => check.ruleId === ruleId)
        .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())[0];

      if (!latestCheck) {
        return {
          status: HEALTH_STATUS.UNKNOWN,
          score: 0
        };
      }

      const result = {
        status: latestCheck.status,
        score: latestCheck.score,
        lastCheck: latestCheck.timestamp,
        trend: this._healthTrends.get(ruleId)?.trend
      };

      // Audit operation
      await this._auditOperation(ruleId, HEALTH_OPERATIONS.GET_STATUS, accessToken, true, undefined, {
        status: result.status,
        score: result.score
      });

      return result;

    } catch (error) {
      this.logError('getHealthStatus', error);
      throw error;
    }
  }

  /**
   * Get health trend analysis
   */
  public async getHealthTrend(ruleId: string, accessToken?: string): Promise<IHealthTrend | null> {
    try {
      this.validateInitialized();
      this.validateRuleId(ruleId);

      // Validate access token
      const tokenValidation = await this._validateAccessToken(accessToken, HEALTH_OPERATIONS.ANALYZE_HEALTH);
      if (!tokenValidation.valid) {
        await this._auditOperation(ruleId, HEALTH_OPERATIONS.ACCESS_DENIED, accessToken, false, tokenValidation.error);
        throw new Error(`Access denied: ${tokenValidation.error ?? 'Invalid token'}`);
      }

      const trend = this._healthTrends.get(ruleId) ?? null;

      // Audit operation
      await this._auditOperation(ruleId, HEALTH_OPERATIONS.ANALYZE_HEALTH, accessToken, true, undefined, {
        hasTrend: !!trend
      });

      return trend;

    } catch (error) {
      this.logError('getHealthTrend', error);
      throw error;
    }
  }

  // ============================================================================
  // PRIVATE HELPER METHODS
  // ============================================================================

  private async _validateAccessToken(
    accessToken?: string, 
    operation?: string
  ): Promise<{ valid: boolean; token?: IAccessToken; error?: string }> {
    try {
      if (!accessToken) {
        // Allow anonymous access for basic operations
        return { valid: true };
      }

      const token = this._accessTokens.get(accessToken);
      if (!token) {
        return { valid: false, error: 'Token not found' };
      }

      if (token.expiresAt < new Date()) {
        this._accessTokens.delete(accessToken);
        return { valid: false, error: 'Token expired' };
      }

      // Check permissions for operation
      if (operation && !token.permissions.includes(operation) && !token.permissions.includes('*')) {
        return { valid: false, error: 'Insufficient permissions' };
      }

      return { valid: true, token };

    } catch (error) {
      this.logError('_validateAccessToken', error);
      return { valid: false, error: 'Token validation failed' };
    }
  }

  private async _generateSystemAccessTokens(): Promise<void> {
    try {
      this.logOperation('_generateSystemAccessTokens', 'start');

      // Generate admin token
      const adminToken: IAccessToken = {
        token: crypto.randomBytes(32).toString('hex'),
        permissions: ['*'],
        expiresAt: new Date(Date.now() + HEALTH_CHECKER_SECURITY_CONFIG.ACCESS_TOKEN_EXPIRY),
        issuedFor: 'system-admin',
        securityLevel: 'admin'
      };

      this._accessTokens.set(adminToken.token, adminToken);

      // Generate read-only token
      const readToken: IAccessToken = {
        token: crypto.randomBytes(32).toString('hex'),
        permissions: [HEALTH_OPERATIONS.GET_STATUS, HEALTH_OPERATIONS.ANALYZE_HEALTH],
        expiresAt: new Date(Date.now() + HEALTH_CHECKER_SECURITY_CONFIG.ACCESS_TOKEN_EXPIRY),
        issuedFor: 'system-reader',
        securityLevel: 'internal'
      };

      this._accessTokens.set(readToken.token, readToken);

      this.logOperation('_generateSystemAccessTokens', 'complete', {
        tokensGenerated: 2
      });

    } catch (error) {
      this.logError('_generateSystemAccessTokens', error);
      throw error;
    }
  }

  private async _analyzeRuleHealth(ruleId: string): Promise<IHealthMetrics> {
    // Simulate comprehensive health analysis
    const baseScore = 85 + Math.random() * 15; // 85-100 range for healthy rules
    const variance = (Math.random() - 0.5) * 20; // ±10 variance

    return {
      availability: Math.max(0, Math.min(100, baseScore + variance)),
      performance: Math.max(0, Math.min(100, baseScore + variance * 0.8)),
      reliability: Math.max(0, Math.min(100, baseScore + variance * 0.6)),
      security: Math.max(0, Math.min(100, baseScore + variance * 0.4)),
      compliance: Math.max(0, Math.min(100, baseScore + variance * 0.7)),
      resourceUtilization: Math.max(0, Math.min(100, 70 + Math.random() * 20)),
      errorRate: Math.max(0, Math.min(100, 100 - (baseScore + variance))), // Inverted
      responseTime: Math.max(50, 200 + Math.random() * 300) // 50-500ms
    };
  }

  private async _identifyHealthIssues(ruleId: string, metrics: IHealthMetrics): Promise<IHealthIssue[]> {
    const issues: IHealthIssue[] = [];

    // Check for performance issues
    if (metrics.performance < this._healthConfig.alertThresholds.performance) {
      issues.push({
        type: 'performance',
        severity: metrics.performance < 50 ? 'critical' : 'medium',
        description: `Performance score is below threshold (${metrics.performance.toFixed(1)}%)`,
        impact: 'Rule execution may be slower than expected',
        recommendation: 'Review rule logic and optimize performance bottlenecks',
        affectedComponents: ['execution-engine', 'rule-optimizer']
      });
    }

    // Check for security issues
    if (metrics.security < this._healthConfig.alertThresholds.security) {
      issues.push({
        type: 'security',
        severity: metrics.security < 70 ? 'critical' : 'high',
        description: `Security score is below threshold (${metrics.security.toFixed(1)}%)`,
        impact: 'Rule may have security vulnerabilities',
        recommendation: 'Conduct security audit and apply security patches',
        affectedComponents: ['security-validator', 'access-control']
      });
    }

    // Check for availability issues
    if (metrics.availability < this._healthConfig.alertThresholds.availability) {
      issues.push({
        type: 'availability',
        severity: metrics.availability < 80 ? 'critical' : 'high',
        description: `Availability is below threshold (${metrics.availability.toFixed(1)}%)`,
        impact: 'Rule may not be consistently available',
        recommendation: 'Review infrastructure and implement redundancy',
        affectedComponents: ['rule-engine', 'infrastructure']
      });
    }

    return issues;
  }

  private _determineHealthStatus(metrics: IHealthMetrics, issues: IHealthIssue[]): HealthStatus {
    const criticalIssues = issues.filter(issue => issue.severity === 'critical');
    const highIssues = issues.filter(issue => issue.severity === 'high');

    if (criticalIssues.length > 0) {
      return HEALTH_STATUS.CRITICAL;
    }

    if (highIssues.length > 0 || metrics.availability < 90) {
      return HEALTH_STATUS.WARNING;
    }

    return HEALTH_STATUS.HEALTHY;
  }

  private _calculateHealthScore(metrics: IHealthMetrics): number {
    // Weighted average of all metrics
    const weights = {
      availability: 0.25,
      performance: 0.20,
      reliability: 0.20,
      security: 0.15,
      compliance: 0.10,
      resourceUtilization: 0.05,
      errorRate: 0.05 // Already inverted
    };

    const score = (
      metrics.availability * weights.availability +
      metrics.performance * weights.performance +
      metrics.reliability * weights.reliability +
      metrics.security * weights.security +
      metrics.compliance * weights.compliance +
      metrics.resourceUtilization * weights.resourceUtilization +
      metrics.errorRate * weights.errorRate
    );

    return Math.round(score * 100) / 100;
  }

  private async _generateHealthRecommendations(
    ruleId: string, 
    metrics: IHealthMetrics, 
    issues: IHealthIssue[]
  ): Promise<string[]> {
    const recommendations: string[] = [];

    // Add issue-specific recommendations
    issues.forEach(issue => {
      recommendations.push(issue.recommendation);
    });

    // Add general recommendations based on metrics
    if (metrics.performance < 90) {
      recommendations.push('Consider implementing performance optimization strategies');
    }

    if (metrics.resourceUtilization > 80) {
      recommendations.push('Monitor resource usage and consider scaling options');
    }

    if (metrics.responseTime > 1000) {
      recommendations.push('Optimize rule execution time to improve responsiveness');
    }

    return Array.from(new Set(recommendations)); // Remove duplicates
  }

  private async _updateHealthTrend(ruleId: string, currentScore: number): Promise<void> {
    try {
      let trend = this._healthTrends.get(ruleId);
      
      if (!trend) {
        trend = {
          ruleId,
          timeWindow: '24h',
          trend: 'stable',
          changeRate: 0,
          predictions: []
        };
        this._healthTrends.set(ruleId, trend);
      }

      // Simple trend analysis (in real implementation, use more sophisticated algorithms)
      const predictions = trend.predictions.slice(-10); // Keep last 10 predictions
      if (predictions.length > 0) {
        const lastScore = predictions[predictions.length - 1].predictedScore;
        const change = currentScore - lastScore;
        trend.changeRate = change;

        if (change > 5) {
          trend.trend = 'improving';
        } else if (change < -5) {
          trend.trend = 'degrading';
        } else if (change < -15) {
          trend.trend = 'critical';
        } else {
          trend.trend = 'stable';
        }
      }

      // Add new prediction
      predictions.push({
        timestamp: new Date(),
        predictedScore: currentScore,
        confidence: 0.85 + Math.random() * 0.15 // 85-100% confidence
      });

      trend.predictions = predictions;

    } catch (error) {
      this.logError('_updateHealthTrend', error);
    }
  }

  private async _performPeriodicHealthChecks(): Promise<void> {
    try {
      this.logOperation('_performPeriodicHealthChecks', 'start', {
        activeChecks: this._healthChecks.size,
        trends: this._healthTrends.size
      });

      // Cleanup old health checks (keep only last 1000)
      if (this._healthChecks.size > 1000) {
        const sortedChecks = Array.from(this._healthChecks.entries())
          .sort((a, b) => a[1].timestamp.getTime() - b[1].timestamp.getTime());
        
        const toRemove = sortedChecks.slice(0, this._healthChecks.size - 1000);
        toRemove.forEach(([checkId]) => this._healthChecks.delete(checkId));
      }

    } catch (error) {
      this.logError('_performPeriodicHealthChecks', error);
    }
  }

  private async _performSecurityScan(): Promise<void> {
    try {
      this.logOperation('_performSecurityScan', 'start');

      // Clean up expired tokens
      const now = new Date();
      let expiredCount = 0;
      
      const tokenEntries = Array.from(this._accessTokens.entries());
      for (let i = 0; i < tokenEntries.length; i++) {
        const [token, tokenData] = tokenEntries[i];
        if (tokenData.expiresAt < now) {
          this._accessTokens.delete(token);
          expiredCount++;
        }
      }

      this.logOperation('_performSecurityScan', 'complete', {
        expiredTokensRemoved: expiredCount,
        activeTokens: this._accessTokens.size
      });

    } catch (error) {
      this.logError('_performSecurityScan', error);
    }
  }

  private async _auditOperation(
    ruleId: string,
    operation: string,
    accessToken?: string,
    success: boolean = true,
    errorMessage?: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    try {
      if (!this._healthConfig.enableAuditTrail) {
        return;
      }

      // Log audit event
      this.logOperation('audit', operation, {
        ruleId,
        success,
        accessToken: accessToken ?? 'anonymous',
        errorMessage,
        metadata
      });

    } catch (error) {
      this.logError('_auditOperation', error);
    }
  }

  // ============================================================================
  // UTILITY METHODS
  // ============================================================================

  private validateInitialized(): void {
    if (!this._initialized) {
      throw new Error('Health checker not initialized');
    }
  }

  private validateRuleId(ruleId: string): void {
    if (!ruleId || typeof ruleId !== 'string') {
      throw new Error('Invalid rule ID: must be a non-empty string');
    }
  }

  private _generateCheckId(): string {
    return `health-check-${Date.now()}-${crypto.randomBytes(8).toString('hex')}`;
  }

  private _calculateAverageHealthScore(): number {
    const checks = Array.from(this._healthChecks.values());
    if (checks.length === 0) return 100;

    const totalScore = checks.reduce((sum, check) => sum + check.score, 0);
    return Math.round((totalScore / checks.length) * 100) / 100;
  }
} 