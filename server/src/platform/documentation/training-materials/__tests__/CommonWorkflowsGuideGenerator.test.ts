/**
 * ============================================================================
 * COMMON WORKFLOWS GUIDE GENERATOR - COMPREHENSIVE TEST SUITE
 * ============================================================================
 * 
 * Enterprise-grade test suite for Common Workflows Guide Generator component.
 * Targets ≥95% coverage across Statements, Branches, Functions, and Lines
 * using surgical precision testing with realistic business scenarios.
 * 
 * Component: common-workflows-guide-generator
 * Task: D-TSK-01.SUB-01.2.IMP-05 - Test Suite Implementation
 * Authority: docs/core/development-standards.md (Workflow Training v2.0)
 * 
 * Test Coverage Goals:
 * - Statements: ≥95%
 * - Branches: ≥95%
 * - Functions: ≥95%
 * - Lines: ≥95%
 * 
 * Test Categories:
 * - Unit Tests: Core functionality and business logic
 * - Integration Tests: BaseTrackingService integration and memory safety
 * - Performance Tests: Resilient timing and resource management
 * - Edge Case Tests: Error handling and boundary conditions
 * - Compliance Tests: OA Framework standards and anti-simplification policy
 * 
 * @validation
 *   typescript-strict: true
 *   memory-safe: true
 *   enterprise-grade: true
 *   coverage-target: 95%
 *   anti-simplification-compliant: true
 * 
 * 📝 VERSION HISTORY
 * @version-history
 *   v1.0.0 - Initial comprehensive test suite implementation
 * ============================================================================
 */

// ============================================================================
// SECTION 1: IMPORTS & TEST DEPENDENCIES
// AI Context: Test framework imports and component dependencies
// ============================================================================

import { describe, test, expect, beforeEach, afterEach, jest } from '@jest/globals';

// Component under test
import { 
  CommonWorkflowsGuideGenerator,
  TCommonWorkflowsGuideGeneratorConfig,
  TWorkflowTrainingContext,
  TWorkflowBestPractice,
  TWorkflowCodeExample,
  TWorkflowTrainingModule,
  TWorkflowAssessment,
  IDocumentationOutput
} from '../CommonWorkflowsGuideGenerator';

// Core tracking types for BaseTrackingService testing
import {
  TTrackingData,
  TValidationResult,
  TMetrics
} from '../../../../../../shared/src/types/platform/tracking/core/tracking-data-types';

import {
  TTrackingConfig
} from '../../../../../../shared/src/types/platform/tracking/tracking-types';

// Documentation generation types
import {
  TDocumentationGenerationOptions
} from '../../../../../../shared/src/types/platform/governance/management-configuration/documentation-generator-types';

// Test utilities for memory-safe testing
import { JestCompatibilityUtils } from '../../../../../../shared/src/base/utils/JestCompatibilityUtils';

// ============================================================================
// SECTION 2: TEST CONFIGURATION & SETUP
// AI Context: Test environment configuration and mock setup
// ============================================================================

/**
 * Test environment detection
 */
const isTestEnvironment = (): boolean => {
  return JestCompatibilityUtils.isTestEnvironment();
};

/**
 * Test-safe delay for timing operations
 */
const testSafeDelay = (ms: number): Promise<void> => {
  if (isTestEnvironment()) {
    return Promise.resolve();
  }
  return new Promise(resolve => setTimeout(resolve, ms));
};

/**
 * Mock configuration factory for testing
 */
const createMockGeneratorConfig = (overrides?: Partial<TCommonWorkflowsGuideGeneratorConfig>): Partial<TCommonWorkflowsGuideGeneratorConfig> => {
  return {
    serviceId: `test-workflows-guide-generator-${Date.now()}`,
    serviceName: 'Test Common Workflows Guide Generator',
    version: '1.0.0-test',
    status: 'initializing',
    timestamp: new Date().toISOString(),
    documentationType: 'training-documentation',
    supportedFormats: ['markdown', 'html', 'pdf', 'json'],
    capabilities: {
      batchProcessing: true,
      realtimeGeneration: false,
      templateCustomization: true,
      multiFormatOutput: true,
      crossReferenceGeneration: true,
      automatedValidation: true,
      versionControlIntegration: false,
      collaborativeEditing: false,
      exportCapabilities: ['pdf', 'html', 'markdown', 'json'],
      integrationCapabilities: ['api', 'webhook', 'batch']
    },
    configuration: {
      timeout: 5000, // Reduced for testing
      retryAttempts: 2, // Reduced for testing
      concurrencyLimit: 5, // Reduced for testing
      loggingLevel: 'debug',
      monitoring: true
    },
    workflowSettings: {
      includeGitWorkflows: true,
      includeTestingWorkflows: true,
      includeDeploymentWorkflows: true,
      includeDevelopmentWorkflows: true,
      includeCodeReviewWorkflows: true,
      includeDocumentationWorkflows: true,
      includeMaintenanceWorkflows: true,
      includeSecurityWorkflows: true
    },
    trainingModuleConfig: {
      enableInteractiveExamples: true,
      enableCodeValidation: true,
      enableProgressTracking: true,
      enableAssessments: true,
      enableCertification: false,
      difficultyLevels: ['beginner', 'intermediate', 'advanced'],
      estimatedCompletionTime: 60 // 1 hour for testing
    },
    outputFormatConfig: {
      supportedFormats: ['markdown', 'html', 'pdf', 'json'],
      defaultFormat: 'markdown',
      includeTableOfContents: true,
      includeIndex: true,
      includeGlossary: true,
      includeAppendices: true,
      customStyling: {}
    },
    performanceConfig: {
      enableBatchProcessing: true,
      maxConcurrentGenerations: 3, // Reduced for testing
      cacheGeneratedContent: true,
      enableIncrementalUpdates: true,
      compressionEnabled: false
    },
    ...overrides
  };
};

/**
 * Enhanced BaseTrackingService mock with interval tracking
 */
const createBaseTrackingServiceMock = () => {
  const intervals: NodeJS.Timeout[] = [];
  const timeouts: NodeJS.Timeout[] = [];

  return {
    intervals,
    timeouts,
    createSafeInterval: jest.fn((callback: () => void, intervalMs: number, name: string) => {
      if (isTestEnvironment()) {
        // In test environment, don't create real intervals
        return `mock-interval-${name}`;
      }
      const interval = setInterval(callback, intervalMs);
      intervals.push(interval);
      return interval;
    }),
    createSafeTimeout: jest.fn((callback: () => void, timeoutMs: number, name: string) => {
      if (isTestEnvironment()) {
        // In test environment, don't create real timeouts
        return `mock-timeout-${name}`;
      }
      const timeout = setTimeout(callback, timeoutMs);
      timeouts.push(timeout);
      return timeout;
    }),
    cleanup: () => {
      intervals.forEach(interval => clearInterval(interval));
      timeouts.forEach(timeout => clearTimeout(timeout));
      intervals.length = 0;
      timeouts.length = 0;
    }
  };
};

/**
 * Mock workflow training context factory
 */
const createMockWorkflowContext = (overrides?: Partial<TWorkflowTrainingContext>): TWorkflowTrainingContext => {
  return {
    workflowId: `test-workflow-${Date.now()}`,
    workflowName: 'Test Git Workflow',
    workflowType: 'git',
    targetAudience: 'beginner',
    prerequisites: ['basic-git-knowledge'],
    learningObjectives: ['Understand Git basics', 'Learn branching strategies'],
    estimatedDuration: 30,
    tools: ['git', 'github'],
    frameworks: ['gitflow'],
    ...overrides
  };
};

// ============================================================================
// SECTION 3: MAIN TEST SUITE
// AI Context: Comprehensive test suite for Common Workflows Guide Generator
// ============================================================================

describe('CommonWorkflowsGuideGenerator', () => {
  let generator: CommonWorkflowsGuideGenerator;
  let mockConfig: Partial<TCommonWorkflowsGuideGeneratorConfig>;
  let baseTrackingMock: ReturnType<typeof createBaseTrackingServiceMock>;

  // ============================================================================
  // TEST SETUP & TEARDOWN
  // ============================================================================

  beforeEach(async () => {
    // Setup test environment
    jest.clearAllMocks();
    
    // Create mock configuration
    mockConfig = createMockGeneratorConfig();
    
    // Create BaseTrackingService mock
    baseTrackingMock = createBaseTrackingServiceMock();
    
    // Create generator instance
    generator = new CommonWorkflowsGuideGenerator(mockConfig);
    
    // Fast-path initialization for tests
    if (isTestEnvironment()) {
      await testSafeDelay(10);
    }
  });

  afterEach(async () => {
    // Cleanup resources
    if (generator) {
      try {
        await generator.shutdown();
      } catch (error) {
        // Ignore shutdown errors in tests
      }
    }
    
    // Cleanup mock intervals/timeouts
    baseTrackingMock.cleanup();
    
    // Clear all mocks
    jest.clearAllMocks();
    
    // Test-safe delay for cleanup
    await testSafeDelay(10);
  });

  // ============================================================================
  // UNIT TESTS - CONSTRUCTOR & INITIALIZATION
  // ============================================================================

  describe('Constructor & Initialization', () => {
    test('should create instance with default configuration', () => {
      const defaultGenerator = new CommonWorkflowsGuideGenerator();

      expect(defaultGenerator).toBeInstanceOf(CommonWorkflowsGuideGenerator);
      expect(defaultGenerator).toBeDefined();
    });

    test('should create instance with custom configuration', () => {
      const customConfig = createMockGeneratorConfig({
        serviceName: 'Custom Workflows Guide Generator',
        version: '2.0.0-custom'
      });

      const customGenerator = new CommonWorkflowsGuideGenerator(customConfig);

      expect(customGenerator).toBeInstanceOf(CommonWorkflowsGuideGenerator);
      expect(customGenerator).toBeDefined();
    });

    test('should implement required interfaces', () => {
      expect(generator).toEqual(expect.objectContaining({
        // ICommonWorkflowsGuideGenerator methods
        initializeCommonWorkflowsGuide: expect.any(Function),
        generateWorkflowDocumentation: expect.any(Function),
        createWorkflowTrainingModule: expect.any(Function),
        generateWorkflowBestPractices: expect.any(Function),
        getWorkflowGuideStatus: expect.any(Function),

        // IWorkflowTrainingService methods
        initializeWorkflowTraining: expect.any(Function),
        processWorkflowTrainingRequest: expect.any(Function),
        getWorkflowTrainingAnalytics: expect.any(Function)
      }));
    });

    test('should initialize resilient timing infrastructure', () => {
      // Test that resilient timing is properly initialized
      expect(generator).toBeDefined();

      // Access private properties for testing (surgical precision testing)
      const resilientTimer = (generator as any)._resilientTimer;
      const metricsCollector = (generator as any)._metricsCollector;

      expect(resilientTimer).toBeDefined();
      expect(metricsCollector).toBeDefined();
    });

    test('should set initial generator status to initializing', () => {
      const generatorStatus = (generator as any)._generatorStatus;
      expect(generatorStatus).toBe('initializing');
    });

    test('should merge configuration with defaults correctly', () => {
      const customConfig = createMockGeneratorConfig({
        workflowSettings: {
          includeGitWorkflows: false,
          includeTestingWorkflows: true,
          includeDeploymentWorkflows: false,
          includeDevelopmentWorkflows: true,
          includeCodeReviewWorkflows: false,
          includeDocumentationWorkflows: true,
          includeMaintenanceWorkflows: false,
          includeSecurityWorkflows: true
        }
      });

      const customGenerator = new CommonWorkflowsGuideGenerator(customConfig);
      const config = (customGenerator as any)._generatorConfig;

      expect(config.workflowSettings.includeGitWorkflows).toBe(false);
      expect(config.workflowSettings.includeTestingWorkflows).toBe(true);
      expect(config.workflowSettings.includeDeploymentWorkflows).toBe(false);
      expect(config.workflowSettings.includeDevelopmentWorkflows).toBe(true);
    });
  });

  // ============================================================================
  // UNIT TESTS - BASETRACKINGSERVICE INTEGRATION
  // ============================================================================

  describe('BaseTrackingService Integration', () => {
    test('should initialize successfully', async () => {
      await expect(generator.initialize()).resolves.not.toThrow();
      expect(generator.isReady()).toBe(true);
    });

    test('should shutdown gracefully', async () => {
      await generator.initialize();
      await expect(generator.shutdown()).resolves.not.toThrow();
      expect(generator.isReady()).toBe(false);
    });

    test('should track workflow generation operations', async () => {
      await generator.initialize();

      const trackingData: TTrackingData = {
        componentId: 'test-workflow-generator',
        status: 'completed',
        timestamp: new Date().toISOString(),
        metadata: {
          phase: 'generation',
          progress: 100,
          priority: 'P1',
          tags: ['workflow', 'generation'],
          custom: {
            workflowGeneration: true,
            executionTime: 1500
          }
        },
        context: {
          environment: 'test',
          user: 'test-user',
          session: 'test-session'
        },
        progress: {
          percentage: 100,
          currentStep: 'completed',
          totalSteps: 1,
          estimatedTimeRemaining: 0
        },
        authority: {
          level: 'user',
          validator: 'test-validator',
          timestamp: new Date().toISOString()
        }
      };

      await expect(generator.track(trackingData)).resolves.not.toThrow();

      // Check that performance metrics were updated
      const performanceMetrics = (generator as any)._performanceMetrics;
      expect(performanceMetrics.totalGenerations).toBeGreaterThan(0);
      expect(performanceMetrics.successfulGenerations).toBeGreaterThan(0);
    });

    test('should validate configuration successfully', async () => {
      await generator.initialize();

      const validationResult = await generator.validate();

      expect(validationResult).toBeDefined();
      expect(validationResult.status).toBe('valid');
      expect(validationResult.overallScore).toBeGreaterThan(80);
      expect(validationResult.componentId).toBe(mockConfig.serviceName);
    });

    test('should handle validation errors gracefully', async () => {
      // Create generator with invalid configuration
      const invalidConfig = createMockGeneratorConfig({
        serviceId: '', // Invalid empty service ID
        workflowSettings: undefined as any // Invalid workflow settings
      });

      const invalidGenerator = new CommonWorkflowsGuideGenerator(invalidConfig);
      await invalidGenerator.initialize();

      const validationResult = await invalidGenerator.validate();

      expect(validationResult.status).toBe('invalid');
      expect(validationResult.errors.length).toBeGreaterThan(0);
      expect(validationResult.overallScore).toBeLessThan(80);
    });
  });

  // ============================================================================
  // UNIT TESTS - WORKFLOW GUIDE GENERATION
  // ============================================================================

  describe('Workflow Guide Generation', () => {
    beforeEach(async () => {
      await generator.initialize();
    });

    test('should generate workflow documentation successfully', async () => {
      const workflowContext = createMockWorkflowContext();
      const options: TDocumentationGenerationOptions = {
        format: 'markdown'
      };

      const result = await generator.generateWorkflowDocumentation(workflowContext, options);

      expect(result).toBeDefined();
      expect(result.id).toBeDefined();
      expect(result.title).toContain('Test Git Workflow');
      expect(result.content).toBeDefined();
      expect(result.format).toBe('markdown');
      expect(result.metadata).toBeDefined();
      expect(result.metadata.workflowId).toBe(workflowContext.workflowId);
      expect(result.metadata.workflowType).toBe(workflowContext.workflowType);
    });

    test('should generate workflow documentation with code examples', async () => {
      const workflowContext = createMockWorkflowContext({
        workflowType: 'testing'
      });
      const options = {
        format: 'markdown',
        includeCodeExamples: true
      } as any;

      const result = await generator.generateWorkflowDocumentation(workflowContext, options);

      expect(result).toBeDefined();
      expect(result.content).toBeDefined();
      expect(result.metadata.workflowType).toBe('testing');
      // Code examples should be included in the workflow content
      expect(result.content.length).toBeGreaterThan(100);
    });

    test('should cache workflow documentation results', async () => {
      const workflowContext = createMockWorkflowContext();
      const options: TDocumentationGenerationOptions = {
        format: 'markdown'
      };

      // First generation
      const result1 = await generator.generateWorkflowDocumentation(workflowContext, options);

      // Second generation (should use cache)
      const result2 = await generator.generateWorkflowDocumentation(workflowContext, options);

      expect(result1.id).toBe(result2.id);
      expect(result1.content).toBe(result2.content);

      // Check cache hit rate increased
      const performanceMetrics = (generator as any)._performanceMetrics;
      expect(performanceMetrics.cacheHitRate).toBeGreaterThan(0);
    });

    test('should handle different output formats', async () => {
      const workflowContext = createMockWorkflowContext();

      // Test markdown format
      const markdownResult = await generator.generateWorkflowDocumentation(workflowContext, { format: 'markdown' });
      expect(markdownResult.format).toBe('markdown');
      expect(markdownResult.content).toContain('#');

      // Test HTML format
      const htmlResult = await generator.generateWorkflowDocumentation(workflowContext, { format: 'html' });
      expect(htmlResult.format).toBe('html');
      expect(htmlResult.content).toContain('<html>');

      // Test JSON format
      const jsonResult = await generator.generateWorkflowDocumentation(workflowContext, { format: 'json' });
      expect(jsonResult.format).toBe('json');
      expect(() => JSON.parse(jsonResult.content)).not.toThrow();
    });

    test('should generate best practices for different workflow types', async () => {
      const gitPractices = await generator.generateWorkflowBestPractices('git');
      expect(gitPractices).toBeDefined();
      expect(Array.isArray(gitPractices)).toBe(true);
      expect(gitPractices.length).toBeGreaterThan(0);
      expect(gitPractices[0]).toHaveProperty('id');
      expect(gitPractices[0]).toHaveProperty('title');
      expect(gitPractices[0]).toHaveProperty('description');

      const testingPractices = await generator.generateWorkflowBestPractices('testing');
      expect(testingPractices).toBeDefined();
      expect(Array.isArray(testingPractices)).toBe(true);
    });

    test('should generate best practices with code examples', async () => {
      const options = {
        includeCodeExamples: true
      } as any;

      const practices = await generator.generateWorkflowBestPractices('git', options);

      expect(practices).toBeDefined();
      expect(practices.length).toBeGreaterThan(0);

      // Check if code examples are included
      const practiceWithExamples = practices.find(p => p.codeExamples && p.codeExamples.length > 0);
      if (practiceWithExamples) {
        expect(practiceWithExamples.codeExamples).toBeDefined();
        expect(practiceWithExamples.codeExamples!.length).toBeGreaterThan(0);
        expect(practiceWithExamples.codeExamples![0]).toHaveProperty('code');
        expect(practiceWithExamples.codeExamples![0]).toHaveProperty('language');
      }
    });

    test('should handle unsupported workflow types gracefully', async () => {
      await expect(generator.generateWorkflowBestPractices('unsupported-type'))
        .rejects.toThrow('Unsupported workflow type');
    });
  });

  // ============================================================================
  // UNIT TESTS - TRAINING MODULE CREATION
  // ============================================================================

  describe('Training Module Creation', () => {
    beforeEach(async () => {
      await generator.initialize();
    });

    test('should create workflow training module successfully', async () => {
      const moduleData: TWorkflowTrainingModule = {
        moduleId: 'test-module-001',
        workflowType: 'git',
        difficultyLevel: 'beginner',
        title: 'Git Basics Training',
        description: 'Learn the fundamentals of Git version control',
        estimatedDuration: 60,
        prerequisites: ['basic-command-line'],
        learningObjectives: ['Understand Git concepts', 'Learn basic Git commands']
      };

      const result = await generator.createWorkflowTrainingModule(moduleData);

      expect(result).toBeDefined();
      expect(result.moduleId).toBe(moduleData.moduleId);
      expect(result.workflowType).toBe(moduleData.workflowType);
      expect(result.title).toBe(moduleData.title);
      expect(result.sections).toBeDefined();
      expect(Array.isArray(result.sections)).toBe(true);
    });

    test('should create training module with interactive elements', async () => {
      // Enable interactive examples in configuration
      const configWithInteractive = createMockGeneratorConfig({
        trainingModuleConfig: {
          enableInteractiveExamples: true,
          enableCodeValidation: true,
          enableProgressTracking: true,
          enableAssessments: false,
          enableCertification: false,
          difficultyLevels: ['beginner'],
          estimatedCompletionTime: 60
        }
      });

      const interactiveGenerator = new CommonWorkflowsGuideGenerator(configWithInteractive);
      await interactiveGenerator.initialize();

      const moduleData: TWorkflowTrainingModule = {
        moduleId: 'test-interactive-module',
        workflowType: 'testing',
        difficultyLevel: 'intermediate',
        title: 'Interactive Testing Training',
        description: 'Hands-on testing workflow training',
        estimatedDuration: 90,
        prerequisites: ['javascript-basics'],
        learningObjectives: ['Write unit tests', 'Understand testing frameworks']
      };

      const result = await interactiveGenerator.createWorkflowTrainingModule(moduleData);

      expect(result.interactiveElements).toBeDefined();
      expect(Array.isArray(result.interactiveElements)).toBe(true);

      await interactiveGenerator.shutdown();
    });

    test('should create training module with assessments', async () => {
      // Enable assessments in configuration
      const configWithAssessments = createMockGeneratorConfig({
        trainingModuleConfig: {
          enableInteractiveExamples: false,
          enableCodeValidation: true,
          enableProgressTracking: true,
          enableAssessments: true,
          enableCertification: false,
          difficultyLevels: ['advanced'],
          estimatedCompletionTime: 120
        }
      });

      const assessmentGenerator = new CommonWorkflowsGuideGenerator(configWithAssessments);
      await assessmentGenerator.initialize();

      const moduleData: TWorkflowTrainingModule = {
        moduleId: 'test-assessment-module',
        workflowType: 'deployment',
        difficultyLevel: 'advanced',
        title: 'Advanced Deployment Training',
        description: 'Master deployment workflows with assessments',
        estimatedDuration: 120,
        prerequisites: ['docker-basics', 'kubernetes-basics'],
        learningObjectives: ['Deploy applications', 'Manage CI/CD pipelines']
      };

      const result = await assessmentGenerator.createWorkflowTrainingModule(moduleData);

      expect(result.assessments).toBeDefined();
      expect(Array.isArray(result.assessments)).toBe(true);
      expect(result.assessments!.length).toBeGreaterThan(0);

      const assessment = result.assessments![0];
      expect(assessment).toHaveProperty('assessmentId');
      expect(assessment).toHaveProperty('moduleId');
      expect(assessment).toHaveProperty('title');
      expect(assessment).toHaveProperty('passingScore');

      await assessmentGenerator.shutdown();
    });

    test('should validate training module data', async () => {
      const invalidModuleData = {
        moduleId: '', // Invalid empty ID
        workflowType: '', // Invalid empty type
        difficultyLevel: 'beginner',
        title: 'Invalid Module',
        description: 'This module has invalid data',
        estimatedDuration: 60,
        prerequisites: [],
        learningObjectives: []
      } as TWorkflowTrainingModule;

      await expect(generator.createWorkflowTrainingModule(invalidModuleData))
        .rejects.toThrow();
    });
  });

  // ============================================================================
  // UNIT TESTS - WORKFLOW TRAINING SERVICE
  // ============================================================================

  describe('Workflow Training Service', () => {
    beforeEach(async () => {
      await generator.initialize();
    });

    test('should initialize workflow training service', async () => {
      const trainingConfig = createMockGeneratorConfig({
        trainingModuleConfig: {
          enableInteractiveExamples: true,
          enableCodeValidation: true,
          enableProgressTracking: true,
          enableAssessments: true,
          enableCertification: true,
          difficultyLevels: ['beginner', 'intermediate', 'advanced'],
          estimatedCompletionTime: 180
        }
      });

      await expect(generator.initializeWorkflowTraining(trainingConfig))
        .resolves.not.toThrow();
    });

    test('should process workflow training request', async () => {
      const trainingRequest = {
        workflowType: 'git',
        targetAudience: 'intermediate' as const,
        includeAssessments: true,
        includeInteractiveElements: true
      };

      const result = await generator.processWorkflowTrainingRequest(trainingRequest);

      expect(result).toBeDefined();
      expect(result.moduleId).toBeDefined();
      expect(result.workflowType).toBe('git');
      expect(result.difficultyLevel).toBe('intermediate');
      expect(result.title).toContain('git');
    });

    test('should get workflow training analytics', () => {
      const analytics = generator.getWorkflowTrainingAnalytics();

      expect(analytics).toBeDefined();
      expect(analytics).toHaveProperty('totalModulesCreated');
      expect(analytics).toHaveProperty('averageCompletionTime');
      expect(analytics).toHaveProperty('popularWorkflowTypes');
      expect(analytics).toHaveProperty('performanceMetrics');
      expect(Array.isArray(analytics.popularWorkflowTypes)).toBe(true);
    });

    test('should get workflow guide status', () => {
      const status = generator.getWorkflowGuideStatus();

      expect(status).toBeDefined();
      expect(status).toHaveProperty('status');
      expect(status).toHaveProperty('activeGenerations');
      expect(status).toHaveProperty('cacheSize');
      expect(status).toHaveProperty('performanceMetrics');
      expect(typeof status.activeGenerations).toBe('number');
      expect(typeof status.cacheSize).toBe('number');
    });
  });

  // ============================================================================
  // INTEGRATION TESTS - PERFORMANCE & EDGE CASES
  // ============================================================================

  describe('Performance & Edge Cases', () => {
    beforeEach(async () => {
      await generator.initialize();
    });

    test('should handle concurrent workflow generations', async () => {
      const workflowContext1 = createMockWorkflowContext({ workflowId: 'concurrent-1', workflowType: 'git' });
      const workflowContext2 = createMockWorkflowContext({ workflowId: 'concurrent-2', workflowType: 'testing' });
      const workflowContext3 = createMockWorkflowContext({ workflowId: 'concurrent-3', workflowType: 'deployment' });

      const promises = [
        generator.generateWorkflowDocumentation(workflowContext1),
        generator.generateWorkflowDocumentation(workflowContext2),
        generator.generateWorkflowDocumentation(workflowContext3)
      ];

      const results = await Promise.all(promises);

      expect(results).toHaveLength(3);
      results.forEach(result => {
        expect(result).toBeDefined();
        expect(result.id).toBeDefined();
        expect(result.content).toBeDefined();
      });
    });

    test('should handle memory cleanup during shutdown', async () => {
      // Generate some content to populate caches
      const workflowContext = createMockWorkflowContext();
      await generator.generateWorkflowDocumentation(workflowContext);

      // Check that caches have content
      const cacheSize = (generator as any)._workflowGuidesCache.size;
      expect(cacheSize).toBeGreaterThan(0);

      // Shutdown should clear caches
      await generator.shutdown();

      const cacheSizeAfterShutdown = (generator as any)._workflowGuidesCache.size;
      expect(cacheSizeAfterShutdown).toBe(0);
    });

    test('should handle cache expiration', async () => {
      // Mock expired cache entry
      const workflowContext = createMockWorkflowContext();
      const cacheKey = 'test-cache-key';
      const expiredResult = {
        generationId: 'expired-id',
        workflowId: workflowContext.workflowId,
        timestamp: new Date(Date.now() - 7200000), // 2 hours ago
        executionTime: 1000,
        status: 'success' as const,
        generatedContent: {
          id: 'expired-content',
          title: 'Expired Content',
          content: 'This content is expired',
          format: 'markdown',
          metadata: {}
        },
        metrics: {
          generationTime: 1000,
          contentLength: 100,
          sectionsGenerated: 1,
          codeExamplesGenerated: 0,
          memoryUsage: 1000000,
          cacheHitRate: 0
        },
        warnings: [],
        errors: []
      };

      // Manually add expired entry to cache
      (generator as any)._workflowGuidesCache.set(cacheKey, expiredResult);

      // Trigger cache cleanup
      (generator as any)._cleanupExpiredCache();

      // Expired entry should be removed
      const cacheAfterCleanup = (generator as any)._workflowGuidesCache;
      expect(cacheAfterCleanup.has(cacheKey)).toBe(false);
    });

    test('should handle errors during generation gracefully', async () => {
      // Create a workflow context that will cause an error
      const invalidContext = createMockWorkflowContext({
        workflowType: 'invalid-type' as any
      });

      // Mock the internal generation method to throw an error
      const originalMethod = (generator as any)._performWorkflowGeneration;
      (generator as any)._performWorkflowGeneration = jest.fn().mockRejectedValue(new Error('Generation failed'));

      await expect(generator.generateWorkflowDocumentation(invalidContext))
        .rejects.toThrow('Generation failed');

      // Restore original method
      (generator as any)._performWorkflowGeneration = originalMethod;
    });

    test('should update performance metrics correctly', () => {
      // Access private method for testing
      (generator as any)._updatePerformanceMetrics();

      const metrics = (generator as any)._performanceMetrics;
      expect(metrics).toBeDefined();
      expect(typeof metrics.totalGenerations).toBe('number');
      expect(typeof metrics.successfulGenerations).toBe('number');
      expect(typeof metrics.failedGenerations).toBe('number');
    });
  });

  // ============================================================================
  // COMPLIANCE TESTS - OA FRAMEWORK STANDARDS
  // ============================================================================

  describe('OA Framework Compliance', () => {
    test('should comply with memory safety requirements', async () => {
      await generator.initialize();

      // Test that memory-safe intervals are created
      const createSafeIntervalSpy = jest.spyOn(generator as any, 'createSafeInterval');

      // Trigger initialization again to test interval creation
      await (generator as any).doInitialize();

      expect(createSafeIntervalSpy).toHaveBeenCalled();
    });

    test('should comply with resilient timing requirements', () => {
      // Test that resilient timing infrastructure is properly initialized
      const resilientTimer = (generator as any)._resilientTimer;
      const metricsCollector = (generator as any)._metricsCollector;

      expect(resilientTimer).toBeDefined();
      expect(metricsCollector).toBeDefined();

      // Test that timing methods are available
      expect(typeof resilientTimer.start).toBe('function');
      expect(typeof metricsCollector.recordTiming).toBe('function');
    });

    test('should comply with anti-simplification policy', async () => {
      await generator.initialize();

      // Test that all required methods are implemented (not simplified)
      expect(generator.initializeCommonWorkflowsGuide).toBeDefined();
      expect(generator.generateWorkflowDocumentation).toBeDefined();
      expect(generator.createWorkflowTrainingModule).toBeDefined();
      expect(generator.generateWorkflowBestPractices).toBeDefined();
      expect(generator.getWorkflowGuideStatus).toBeDefined();
      expect(generator.initializeWorkflowTraining).toBeDefined();
      expect(generator.processWorkflowTrainingRequest).toBeDefined();
      expect(generator.getWorkflowTrainingAnalytics).toBeDefined();

      // Test that configuration includes all required settings
      const config = (generator as any)._generatorConfig;
      expect(config.workflowSettings).toBeDefined();
      expect(config.trainingModuleConfig).toBeDefined();
      expect(config.outputFormatConfig).toBeDefined();
      expect(config.performanceConfig).toBeDefined();
    });

    test('should handle test environment detection', () => {
      const isTestMode = (generator as any)._isTestMode();
      expect(typeof isTestMode).toBe('boolean');
      expect(isTestMode).toBe(true); // Should be true in test environment
    });
  });

  // ============================================================================
  // SURGICAL PRECISION TESTS - TARGET SPECIFIC UNCOVERED LINES
  // ============================================================================

  describe('Surgical Precision Coverage Tests', () => {
    beforeEach(async () => {
      await generator.initialize();
    });

    test('should handle workflow generation with empty cache key', async () => {
      // Target cache key generation edge cases
      const workflowContext = createMockWorkflowContext({
        workflowId: '',
        workflowName: '',
        workflowType: 'git'
      });

      const result = await generator.generateWorkflowDocumentation(workflowContext);
      expect(result).toBeDefined();
    });

    test('should handle format conversion edge cases', async () => {
      const workflowContext = createMockWorkflowContext();

      // Test PDF format (should fallback to markdown)
      const pdfResult = await generator.generateWorkflowDocumentation(workflowContext, { format: 'pdf' });
      expect(pdfResult.format).toBe('pdf');

      // Test unknown format (should fallback to markdown)
      const unknownResult = await generator.generateWorkflowDocumentation(workflowContext, { format: 'unknown' as any });
      expect(unknownResult.format).toBe('unknown');
    });

    test('should handle validation with missing workflow settings', async () => {
      // Create generator with undefined workflow settings to trigger validation warnings
      const invalidConfig = createMockGeneratorConfig();
      delete (invalidConfig as any).workflowSettings;

      const invalidGenerator = new CommonWorkflowsGuideGenerator(invalidConfig);
      await invalidGenerator.initialize();

      const validationResult = await invalidGenerator.validate();
      // The validation should still pass but may have warnings
      expect(validationResult.status).toBeDefined();
      expect(validationResult.overallScore).toBeGreaterThan(0);

      await invalidGenerator.shutdown();
    });

    test('should handle performance metrics edge cases', async () => {
      // Test performance metrics with zero values
      const performanceMetrics = (generator as any)._performanceMetrics;
      performanceMetrics.totalGenerations = 0;
      performanceMetrics.cacheHitRate = 0;

      // Trigger performance metrics update
      (generator as any)._updatePerformanceMetrics();

      expect(performanceMetrics.cacheHitRate).toBe(0);
    });

    test('should handle cache cleanup with mixed expired and valid entries', async () => {
      const workflowContext = createMockWorkflowContext();

      // Add a valid entry
      await generator.generateWorkflowDocumentation(workflowContext);

      // Manually add an expired entry
      const expiredResult = {
        generationId: 'expired-test',
        workflowId: 'expired-workflow',
        timestamp: new Date(Date.now() - 7200000), // 2 hours ago
        executionTime: 1000,
        status: 'success' as const,
        generatedContent: {
          id: 'expired',
          title: 'Expired',
          content: 'Expired content',
          format: 'markdown',
          metadata: {}
        },
        metrics: {
          generationTime: 1000,
          contentLength: 100,
          sectionsGenerated: 1,
          codeExamplesGenerated: 0,
          memoryUsage: 1000000,
          cacheHitRate: 0
        },
        warnings: [],
        errors: []
      };

      (generator as any)._workflowGuidesCache.set('expired-key', expiredResult);

      const initialCacheSize = (generator as any)._workflowGuidesCache.size;
      expect(initialCacheSize).toBeGreaterThan(1);

      // Trigger cleanup
      (generator as any)._cleanupExpiredCache();

      const finalCacheSize = (generator as any)._workflowGuidesCache.size;
      expect(finalCacheSize).toBeLessThan(initialCacheSize);
    });

    test('should handle workflow generation failure scenarios', async () => {
      const workflowContext = createMockWorkflowContext();

      // Mock the content generation to throw an error
      const originalMethod = (generator as any)._generateWorkflowContent;
      (generator as any)._generateWorkflowContent = jest.fn().mockRejectedValue(new Error('Content generation failed'));

      const result = await generator.generateWorkflowDocumentation(workflowContext);

      // Should return failed result with error content
      expect(result).toBeDefined();
      expect(result.title).toContain('Failed');

      // Restore original method
      (generator as any)._generateWorkflowContent = originalMethod;
    });

    test('should handle training module validation edge cases', async () => {
      // Test with missing workflowType
      const invalidModule = {
        moduleId: 'test-module',
        workflowType: '',
        difficultyLevel: 'beginner',
        title: 'Test Module',
        description: 'Test description',
        estimatedDuration: 60,
        prerequisites: [],
        learningObjectives: []
      } as TWorkflowTrainingModule;

      await expect(generator.createWorkflowTrainingModule(invalidModule))
        .rejects.toThrow('Workflow type is required');
    });

    test('should handle analytics with empty cache', () => {
      // Clear cache to test empty state
      (generator as any)._workflowGuidesCache.clear();

      const analytics = generator.getWorkflowTrainingAnalytics();

      expect(analytics.popularWorkflowTypes).toEqual([]);
      expect(analytics.totalModulesCreated).toBeDefined();
    });

    test('should handle concurrent generation limit exceeded', async () => {
      // Set a very low concurrent generation limit
      (generator as any)._generatorConfig.performanceConfig.maxConcurrentGenerations = 1;

      const validationResult = await generator.validate();

      // Should not have errors about concurrent generations since we haven't exceeded the limit yet
      expect(validationResult).toBeDefined();
    });

    test('should handle large cache size warning', async () => {
      // Simulate large cache by adding many entries
      const cache = (generator as any)._workflowGuidesCache;
      for (let i = 0; i < 101; i++) {
        cache.set(`test-key-${i}`, {
          generationId: `test-${i}`,
          workflowId: `workflow-${i}`,
          timestamp: new Date(),
          executionTime: 1000,
          status: 'success',
          generatedContent: { id: `content-${i}`, title: 'Test', content: 'Test content', format: 'markdown', metadata: {} },
          metrics: { generationTime: 1000, contentLength: 100, sectionsGenerated: 1, codeExamplesGenerated: 0, memoryUsage: 1000000, cacheHitRate: 0 },
          warnings: [],
          errors: []
        });
      }

      const validationResult = await generator.validate();
      expect(validationResult.warnings.some(w => w.includes('Large cache size'))).toBe(true);
    });

    test('should handle active generations exceeding limit', async () => {
      // Set very low limit and simulate active generations
      (generator as any)._generatorConfig.performanceConfig.maxConcurrentGenerations = 1;

      // Simulate active generations exceeding limit
      const activeGenerations = (generator as any)._activeGenerations;
      activeGenerations.set('gen1', Promise.resolve({}));
      activeGenerations.set('gen2', Promise.resolve({}));

      const validationResult = await generator.validate();
      expect(validationResult.errors.some(e => e.includes('Too many active generations'))).toBe(true);

      // Clean up
      activeGenerations.clear();
    });

    test('should handle workflow generation with existing active generation', async () => {
      const workflowContext = createMockWorkflowContext();
      const options: TDocumentationGenerationOptions = { format: 'markdown' };

      // Start first generation
      const firstGeneration = generator.generateWorkflowDocumentation(workflowContext, options);

      // Start second generation with same context (should wait for first)
      const secondGeneration = generator.generateWorkflowDocumentation(workflowContext, options);

      const [result1, result2] = await Promise.all([firstGeneration, secondGeneration]);

      expect(result1.id).toBe(result2.id); // Should be same result
    });

    test('should handle HTML content formatting', async () => {
      const workflowContext = createMockWorkflowContext();

      const result = await generator.generateWorkflowDocumentation(workflowContext, { format: 'html' });

      expect(result.format).toBe('html');
      expect(result.content).toContain('<html>');
      expect(result.content).toContain('<h1>');
    });

    test('should handle workflow sections generation with different types', async () => {
      const deploymentContext = createMockWorkflowContext({
        workflowType: 'deployment',
        workflowName: 'Deployment Workflow'
      });

      const result = await generator.generateWorkflowDocumentation(deploymentContext);

      expect(result.metadata.workflowType).toBe('deployment');
      expect(result.content).toContain('Deployment Workflow');
    });

    test('should handle training module with all features enabled', async () => {
      // Enable all training features
      const fullFeaturesConfig = createMockGeneratorConfig({
        trainingModuleConfig: {
          enableInteractiveExamples: true,
          enableCodeValidation: true,
          enableProgressTracking: true,
          enableAssessments: true,
          enableCertification: true,
          difficultyLevels: ['beginner', 'intermediate', 'advanced'],
          estimatedCompletionTime: 180
        }
      });

      const fullFeaturesGenerator = new CommonWorkflowsGuideGenerator(fullFeaturesConfig);
      await fullFeaturesGenerator.initialize();

      const moduleData: TWorkflowTrainingModule = {
        moduleId: 'full-features-module',
        workflowType: 'security',
        difficultyLevel: 'advanced',
        title: 'Advanced Security Training',
        description: 'Comprehensive security workflow training',
        estimatedDuration: 180,
        prerequisites: ['security-basics', 'compliance-knowledge'],
        learningObjectives: ['Implement security workflows', 'Understand compliance requirements']
      };

      const result = await fullFeaturesGenerator.createWorkflowTrainingModule(moduleData);

      expect(result.interactiveElements).toBeDefined();
      expect(result.assessments).toBeDefined();
      expect(result.sections).toBeDefined();

      await fullFeaturesGenerator.shutdown();
    });

    test('should handle performance metrics calculation with non-zero values', async () => {
      // Set up performance metrics with actual values
      const performanceMetrics = (generator as any)._performanceMetrics;
      performanceMetrics.totalGenerations = 10;
      performanceMetrics.cacheHitRate = 5;

      // Trigger performance metrics update
      (generator as any)._updatePerformanceMetrics();

      expect(performanceMetrics.cacheHitRate).toBe(50); // 5/10 * 100
    });

    test('should handle workflow best practices with different priorities', async () => {
      const practices = await generator.generateWorkflowBestPractices('development');

      expect(practices).toBeDefined();
      expect(practices.length).toBeGreaterThan(0);
      expect(practices[0].priority).toBeDefined();
      expect(['low', 'medium', 'high']).toContain(practices[0].priority);
    });

    test('should handle code examples generation for practices', async () => {
      const practice: TWorkflowBestPractice = {
        id: 'test-practice',
        title: 'Test Practice',
        description: 'Test description',
        category: 'general',
        priority: 'high',
        applicableWorkflows: ['git']
      };

      const codeExamples = await (generator as any)._generateCodeExamplesForPractice(practice, 'git');

      expect(codeExamples).toBeDefined();
      expect(Array.isArray(codeExamples)).toBe(true);
      expect(codeExamples.length).toBeGreaterThan(0);
      expect(codeExamples[0]).toHaveProperty('code');
      expect(codeExamples[0]).toHaveProperty('language');
    });
  });
});
