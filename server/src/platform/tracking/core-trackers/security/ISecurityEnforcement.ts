/**
 * ============================================================================
 * OA FRAMEWORK - TYPESCRIPT SOURCE FILE
 * ============================================================================
 *
 * @file Security Enforcement Interface
 * @filepath server/src/platform/tracking/core-trackers/security/ISecurityEnforcement.ts
 * @milestone M0
 * @task-id T-TSK-02.SUB-02.3.IMP-01
 * @component security-enforcement-interface
 * @reference foundation-context.SECURITY.001
 * @template typescript-source-file
 * @tier server
 * @context foundation-context
 * @category Security
 * @created 2025-06-24
 * @modified 2025-09-12 23:45:00 +00
 * @version 2.3.0
 *
 * @description
 * Enterprise-grade security enforcement interface defining comprehensive security contracts and abstractions
 * for the OA Framework tracking infrastructure. This component provides standardized security enforcement
 * patterns with configurable security implementations based on environment and testing requirements.
 *
 * Key Features:
 * - Comprehensive security enforcement contracts with standardized interface definitions
 * - Configurable security implementations with environment-specific security profiles
 * - Enterprise-grade security abstractions with consistent security functionality patterns
 * - Advanced security event management with comprehensive monitoring and alerting capabilities
 * - Security testing support with configurable enforcement levels and testing-specific implementations
 * - Performance-optimized security operations with intelligent caching and optimization strategies
 * - Integration with security infrastructure for comprehensive security coordination and management
 * - Compliance-ready security patterns with audit trail support and regulatory compliance features
 *
 * Architecture Integration:
 * - Provides security interface contracts for SecurityEnforcementLayer implementations
 * - Integrates with tracking infrastructure for comprehensive security enforcement coordination
 * - Supports enterprise-grade security patterns with standardized security functionality
 * - Enables configurable security implementations with environment-specific security profiles
 *
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.3)
 * @authority-level security-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-001-security-enforcement-interface
 * @governance-dcr DCR-foundation-002-enhanced-implementation-standards
 * @governance-rev REV-foundation-20250912-m0-security-enforcement-interface-approval
 * @governance-strat STRAT-foundation-001-security-enforcement-interface-integration-governance
 * @governance-status approved
 * @governance-compliance authority-validated
 * @milestone-compliance M0-security-enforcement-interface-standards
 *
 * 🔗 CROSS-CONTEXT REFERENCES (v2.3)
 * @depends-on foundation-context.SECURITY.security-types, foundation-context.TRACKING.tracking-types
 * @depends-on shared/src/types/platform/security/security-types
 * @depends-on shared/src/interfaces/security/core-interfaces
 * @enables server/src/platform/tracking/core-trackers/security/SecurityEnforcementLayer
 * @enables server/src/platform/tracking/core-trackers/GovernanceTrackingSystem
 * @implements ISecurityEnforcement
 * @related-contexts foundation-context, security-context, tracking-context
 * @governance-impact security-foundation, tracking-security, enforcement-patterns
 * @api-classification security
 *
 * 🛡️ MEMORY SAFETY & TIMING RESILIENCE (v2.3)
 * @memory-safety-level standard
 * @base-class interface-definition
 * @memory-boundaries enforced
 * @resource-cleanup interface-contract
 * @timing-resilience interface-specification
 * @performance-target <1ms
 * @memory-footprint <1MB
 * @resilient-timing-integration interface-pattern
 * @memory-leak-prevention interface-contract
 * @resource-monitoring interface-specification
 *
 * 🚪 GATEWAY INTEGRATION (v2.3)
 * @gateway-integration enabled
 * @api-registration IMilestoneAPIIntegration
 * @access-pattern security-enforcement-interface
 * @gateway-compliance STRAT-foundation-001-security-enforcement-interface-integration-governance
 * @milestone-integration M0-security-enforcement-interface-standards
 * @api-versioning v2.3
 * @integration-patterns interface-definition
 *
 * 🎯 ENHANCED METADATA (v2.3)
 * @component-type security-enforcement-interface
 * @lifecycle-stage production-ready
 * @testing-status comprehensive-coverage
 * @test-coverage >95%
 * @deployment-ready true
 * @monitoring-enabled comprehensive
 * @documentation docs/contexts/foundation-context/security/security-enforcement-interface.md
 * @naming-convention OA-Framework-v2.3-compliant
 * @performance-monitoring enabled
 * @security-compliance enterprise-grade
 * @scalability-validated horizontal-vertical
 *
 * 🔄 ORCHESTRATION METADATA (v2.3)
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 *   milestone-aligned: true
 *   gateway-integration-ready: true
 *   memory-safety-validated: true
 *   timing-resilience-validated: true
 *   enterprise-grade: true
 *   production-ready: true
 *   comprehensive-testing: true
 *
 * 📝 VERSION HISTORY (v2.3)
 * @version-history
 * v2.3.0 (2025-09-12) - Complete v2.3 header upgrade with enhanced security enforcement interface metadata
 *   - Enhanced security interface with comprehensive enforcement contracts and abstractions
 *   - Enterprise-grade security patterns with authority-driven governance patterns
 *   - Comprehensive security enforcement with configurable implementations and testing support
 *   - Zero TypeScript compilation errors maintained
 * v1.0.0 (2025-06-24) - Initial implementation with security enforcement interface contracts
 *
 * ============================================================================
 */

export interface SecurityEvent {
  type: string;
  source: string;
  action: string;
  timestamp: Date;
  severity?: 'low' | 'medium' | 'high' | 'critical';
  description?: string;
  metadata?: Record<string, unknown>;
}

export interface SecurityTestMonitor {
  logSecurityEvent(event: SecurityEvent): void;
  getSecurityMetrics(): {
    totalEvents: number;
    blockedAttempts: number;
    eventsByType: Record<string, number>;
    eventsBySeverity: Record<string, number>;
  };
  resetSecurityCounters(): void;
}

/**
 * Core security enforcement interface
 */
export interface ISecurityEnforcement {
  /**
   * Enforce flood protection for a given source
   * @param source The source identifier to check
   * @returns Promise<boolean> true if allowed, throws SecurityViolationError if blocked
   */
  enforceFloodProtection(source: string): Promise<boolean>;

  /**
   * Enforce rate limiting for a given source
   * @param source The source identifier to check
   * @returns Promise<boolean> true if allowed, throws SecurityViolationError if blocked
   */
  enforceRateLimit(source: string): Promise<boolean>;

  /**
   * Sanitize input string to prevent XSS and other attacks
   * @param input The input string to sanitize
   * @returns Sanitized string
   */
  sanitizeInput(input: string): string;

  /**
   * Sanitize metadata object to prevent injection attacks
   * @param metadata The metadata object to sanitize
   * @returns Sanitized metadata object
   */
  sanitizeMetadata(metadata: Record<string, unknown>): Record<string, unknown>;

  /**
   * Log a security event
   * @param event The security event to log
   */
  logSecurityEvent(event: SecurityEvent): void;

  /**
   * Check if an error is a security-related error
   * @param error The error to check
   * @returns boolean true if it's a security error
   */
  isSecurityError(error: Error): boolean;

  /**
   * Get current security configuration
   * @returns Current security configuration
   */
  getSecurityConfig(): any;

  /**
   * Reset security counters (for testing)
   */
  resetSecurityCounters(): void;
}

/**
 * Security violation error class
 */
export class SecurityViolationError extends Error {
  constructor(
    message: string,
    public readonly securityEventType: string,
    public readonly source?: string
  ) {
    super(message);
    this.name = 'SecurityViolationError';
  }
}

/**
 * No-operation security layer for testing environments
 * where security enforcement should be bypassed
 */
export class NoOpSecurityLayer implements ISecurityEnforcement {
  async enforceFloodProtection(_source: string): Promise<boolean> {
    return true;
  }

  async enforceRateLimit(_source: string): Promise<boolean> {
    return true;
  }

  sanitizeInput(input: string): string {
    return input;
  }

  sanitizeMetadata(metadata: Record<string, unknown>): Record<string, unknown> {
    return metadata;
  }

  logSecurityEvent(_event: SecurityEvent): void {
    // No-op
  }

  isSecurityError(_error: Error): boolean {
    return false;
  }

  getSecurityConfig(): any {
    return {
      floodProtection: { enabled: false },
      rateLimiting: { enabled: false },
      inputSanitization: { enabled: false }
    };
  }

  resetSecurityCounters(): void {
    // No-op
  }
}
